package com.zjkj.aigc.domain.task.service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RSet;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 增强的任务队列服务 - 支持基于businessId的公平调度
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcTaskQueueService {
    private final RedissonClient redissonClient;

    /**
     * 生成业务线队列Key
     * 格式：aigc:task:queue:{taskType}:{modelName}:{businessId}
     */
    private String getBusinessQueueKey(String taskType, String modelName, String businessId) {
        return String.format("aigc:task:queue:%s:%s:%s", taskType, modelName, businessId);
    }

    /**
     * 生成活跃业务线集合Key
     */
    private String getActiveBusinessSetKey(String taskType, String modelName) {
        return String.format("aigc:task:active_business:%s:%s", taskType, modelName);
    }

    /**
     * 生成轮询索引Key
     */
    private String getRoundRobinIndexKey(String modelKey) {
        return String.format("aigc:task:round_robin_index:%s", modelKey);
    }

    /**
     * 生成模型Key
     */
    private String getModelKey(String taskType, String modelName) {
        return String.format("%s:%s", taskType, modelName);
    }

    /**
     * 计算任务分数
     */
    private double getTaskScore(Long id, Integer taskPriority) {
        if (taskPriority == null) {
            taskPriority = 0;
        }
        // 优先级越高（数值越大），分数越小，排序越靠前
        return System.currentTimeMillis() - (taskPriority * 1000000L) + id;
    }

    /**
     * 添加任务到队列
     *
     * @param aigcTask 任务信息
     */
    public void add(AigcTask aigcTask) {
        String businessId = aigcTask.getBusinessId();
        if (!StringUtils.hasText(businessId)) {
            throw new IllegalArgumentException("businessId不能为空");
        }

        double taskScore = getTaskScore(aigcTask.getId(), aigcTask.getTaskPriority());
        String queueKey = getBusinessQueueKey(aigcTask.getTaskType(), aigcTask.getModelName(), businessId);
        RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(queueKey);
        boolean added = sortedSet.add(taskScore, aigcTask.getId());

        // 注册业务线到活跃列表
        registerBusinessId(aigcTask.getTaskType(), aigcTask.getModelName(), businessId);

        log.info("添加任务到队列, taskId:{}, businessId:{}, queueKey:{}, added:{}",
                aigcTask.getTaskId(), businessId, queueKey, added);
    }

    /**
     * 批量添加任务
     *
     * @param aigcTaskList 任务信息列表
     */
    public void batchAdd(Collection<AigcTask> aigcTaskList) {
        if (CollectionUtils.isEmpty(aigcTaskList)) {
            return;
        }

        // 按 taskType + modelName + businessId 分组
        Map<String, List<AigcTask>> groupedTasks = aigcTaskList.stream()
                .collect(Collectors.groupingBy(task -> {
                    String businessId = task.getBusinessId();
                    if (!StringUtils.hasText(businessId)) {
                        throw new IllegalArgumentException("businessId不能为空, taskId: " + task.getTaskId());
                    }
                    return getBusinessQueueKey(task.getTaskType(), task.getModelName(), businessId);
                }));

        groupedTasks.forEach((queueKey, tasks) -> {
            Map<Long, Double> taskMap = tasks.stream()
                    .collect(Collectors.toMap(
                            AigcTask::getId,
                            task -> getTaskScore(task.getId(), task.getTaskPriority()),
                            (existing, replacement) -> existing
                    ));

            RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(queueKey);
            int addedCount = sortedSet.addAll(taskMap);

            // 注册业务线
            if (!tasks.isEmpty()) {
                AigcTask firstTask = tasks.get(0);
                registerBusinessId(firstTask.getTaskType(), firstTask.getModelName(), firstTask.getBusinessId());
            }

            log.info("批量添加任务到队列, queueKey:{}, taskCount:{}, addedCount:{}",
                    queueKey, tasks.size(), addedCount);
        });
    }

    /**
     * 添加任务到队列（如果不存在）
     *
     * @param aigcTask 任务信息
     */
    public void addIfAbsent(AigcTask aigcTask) {
        String businessId = aigcTask.getBusinessId();
        if (!StringUtils.hasText(businessId)) {
            throw new IllegalArgumentException("businessId不能为空");
        }

        String queueKey = getBusinessQueueKey(aigcTask.getTaskType(), aigcTask.getModelName(), businessId);
        RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(queueKey);

        double taskScore = getTaskScore(aigcTask.getId(), aigcTask.getTaskPriority());
        boolean added = sortedSet.addIfAbsent(taskScore, aigcTask.getId());

        if (added) {
            registerBusinessId(aigcTask.getTaskType(), aigcTask.getModelName(), businessId);
        }

        log.info("添加任务到队列（如果不存在）, queueKey:{}, id:{}, added:{}", queueKey, aigcTask.getId(), added);
    }

    /**
     * 注册业务线到活跃列表
     */
    private void registerBusinessId(String taskType, String modelName, String businessId) {
        String activeSetKey = getActiveBusinessSetKey(taskType, modelName);
        RSet<String> activeSet = redissonClient.getSet(activeSetKey);
        boolean added = activeSet.add(businessId);

        if (added) {
            // 设置过期时间，防止内存泄漏
            activeSet.expire(24, TimeUnit.HOURS);
            log.debug("注册业务线到活跃列表, activeSetKey:{}, businessId:{}", activeSetKey, businessId);
        }
    }

    /**
     * 从活跃列表中移除业务线
     */
    private void removeBusinessIdFromActive(String taskType, String modelName, String businessId) {
        String activeSetKey = getActiveBusinessSetKey(taskType, modelName);
        RSet<String> activeSet = redissonClient.getSet(activeSetKey);
        boolean removed = activeSet.remove(businessId);

        if (removed) {
            log.debug("从活跃列表移除业务线, activeSetKey:{}, businessId:{}", activeSetKey, businessId);
        }
    }

    /**
     * 获取活跃的业务线列表
     */
    private Set<String> getActiveBusinessIds(String taskType, String modelName) {
        String activeSetKey = getActiveBusinessSetKey(taskType, modelName);
        RSet<String> activeSet = redissonClient.getSet(activeSetKey);
        return activeSet.readAll();
    }

    /**
     * 获取轮询索引
     */
    private long getRoundRobinIndex(String modelKey) {
        String indexKey = getRoundRobinIndexKey(modelKey);
        RAtomicLong atomicLong = redissonClient.getAtomicLong(indexKey);
        return atomicLong.get();
    }

    /**
     * 更新轮询索引
     */
    private void updateRoundRobinIndex(String modelKey, long newIndex) {
        String indexKey = getRoundRobinIndexKey(modelKey);
        RAtomicLong atomicLong = redissonClient.getAtomicLong(indexKey);
        atomicLong.set(newIndex);

        // 设置过期时间
        atomicLong.expire(1, TimeUnit.HOURS);
    }

    /**
     * 公平调度获取任务 - 核心方法
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 任务ID
     */
    public Long pop(String taskType, String modelName) {
        // 获取所有活跃的业务线
        Set<String> activeBusinessIds = getActiveBusinessIds(taskType, modelName);
        if (activeBusinessIds.isEmpty()) {
            log.debug("没有活跃的业务线, taskType:{}, modelName:{}", taskType, modelName);
            return null;
        }

        // 获取并更新轮询索引
        String modelKey = getModelKey(taskType, modelName);
        long currentIndex = getRoundRobinIndex(modelKey);

        // 转换为列表以支持索引访问
        List<String> businessIdList = new ArrayList<>(activeBusinessIds);
        Collections.sort(businessIdList); // 确保顺序一致性

        // 轮询获取任务
        int startIndex = (int) (currentIndex % businessIdList.size());
        for (int i = 0; i < businessIdList.size(); i++) {
            int currentIdx = (startIndex + i) % businessIdList.size();
            String businessId = businessIdList.get(currentIdx);

            Long taskId = popFromBusinessQueue(taskType, modelName, businessId);
            if (taskId != null) {
                // 更新轮询索引到下一个业务线
                updateRoundRobinIndex(modelKey, currentIdx + 1);

                log.info("公平调度获取任务成功, taskType:{}, modelName:{}, businessId:{}, taskId:{}, nextIndex:{}",
                        taskType, modelName, businessId, taskId, (currentIdx + 1) % businessIdList.size());
                return taskId;
            }
        }

        log.debug("所有业务线队列都为空, taskType:{}, modelName:{}", taskType, modelName);
        return null;
    }

    /**
     * 从指定业务线队列获取任务
     */
    private Long popFromBusinessQueue(String taskType, String modelName, String businessId) {
        String queueKey = getBusinessQueueKey(taskType, modelName, businessId);
        RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(queueKey);

        Long taskId = sortedSet.pollFirst();
        if (taskId != null) {
            log.debug("从业务线队列获取任务, queueKey:{}, taskId:{}", queueKey, taskId);

            // 如果队列为空，从活跃列表中移除
            if (sortedSet.size() == 0) {
                removeBusinessIdFromActive(taskType, modelName, businessId);
            }
        }

        return taskId;
    }

    /**
     * 批量更新任务（更新任务优先级等信息）
     *
     * @param aigcTaskList 任务信息列表
     */
    public void batchUpdate(Collection<AigcTask> aigcTaskList) {
        if (CollectionUtils.isEmpty(aigcTaskList)) {
            return;
        }

        // 按 taskType + modelName + businessId 分组
        Map<String, List<AigcTask>> groupedTasks = aigcTaskList.stream()
                .collect(Collectors.groupingBy(task -> {
                    String businessId = task.getBusinessId();
                    if (!StringUtils.hasText(businessId)) {
                        throw new IllegalArgumentException("businessId不能为空, taskId: " + task.getTaskId());
                    }
                    return getBusinessQueueKey(task.getTaskType(), task.getModelName(), businessId);
                }));

        groupedTasks.forEach((queueKey, tasks) -> {
            RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(queueKey);

            // 更新每个任务的分数（主要是优先级变化）
            for (AigcTask task : tasks) {
                double newScore = getTaskScore(task.getId(), task.getTaskPriority());
                Double currentScore = sortedSet.getScore(task.getId());

                if (currentScore != null) {
                    // 任务存在，更新分数
                    sortedSet.add(newScore, task.getId());
                    log.debug("更新任务分数, queueKey:{}, taskId:{}, oldScore:{}, newScore:{}",
                             queueKey, task.getId(), currentScore, newScore);
                } else {
                    // 任务不存在，添加到队列
                    sortedSet.add(newScore, task.getId());
                    log.debug("任务不在队列中，重新添加, queueKey:{}, taskId:{}, newScore:{}",
                             queueKey, task.getId(), newScore);
                }
            }

            log.info("批量更新任务到队列, queueKey:{}, taskCount:{}", queueKey, tasks.size());
        });
    }

    /**
     * 批量移除任务
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @param ids       任务ID列表
     */
    public void batchRemove(String taskType, String modelName, Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // 需要遍历所有可能的业务线队列
        Set<String> activeBusinessIds = getActiveBusinessIds(taskType, modelName);

        for (String businessId : activeBusinessIds) {
            String queueKey = getBusinessQueueKey(taskType, modelName, businessId);
            RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(queueKey);
            boolean removed = sortedSet.removeAll(ids);

            if (removed) {
                log.info("批量移除任务, queueKey:{}, ids:{}", queueKey, ids);

                // 检查队列是否为空
                if (sortedSet.size() == 0) {
                    removeBusinessIdFromActive(taskType, modelName, businessId);
                }
            }
        }
    }

    /**
     * 获取队列统计信息
     */
    public Map<String, Object> getQueueStats(String taskType, String modelName) {
        Map<String, Object> stats = new HashMap<>();
        Set<String> activeBusinessIds = getActiveBusinessIds(taskType, modelName);

        Map<String, Long> businessQueueSizes = new HashMap<>();
        long totalTasks = 0;

        for (String businessId : activeBusinessIds) {
            String queueKey = getBusinessQueueKey(taskType, modelName, businessId);
            RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(queueKey);
            long size = sortedSet.size();
            businessQueueSizes.put(businessId, size);
            totalTasks += size;
        }

        String modelKey = getModelKey(taskType, modelName);
        long currentIndex = getRoundRobinIndex(modelKey);

        stats.put("taskType", taskType);
        stats.put("modelName", modelName);
        stats.put("activeBusinessCount", activeBusinessIds.size());
        stats.put("activeBusinessIds", activeBusinessIds);
        stats.put("businessQueueSizes", businessQueueSizes);
        stats.put("totalTasks", totalTasks);
        stats.put("currentRoundRobinIndex", currentIndex);

        return stats;
    }

    /**
     * 清理指定模型的缓存和索引
     */
    public void clearModelCache(String taskType, String modelName) {
        String modelKey = getModelKey(taskType, modelName);
        String activeSetKey = getActiveBusinessSetKey(taskType, modelName);
        String indexKey = getRoundRobinIndexKey(modelKey);

        // 清理活跃业务线集合
        redissonClient.getSet(activeSetKey).delete();

        // 重置轮询索引
        redissonClient.getAtomicLong(indexKey).delete();

        log.info("清理模型缓存, taskType:{}, modelName:{}", taskType, modelName);
    }

    /**
     * 手动刷新活跃业务线列表
     */
    public void refreshActiveBusinessIds(String taskType, String modelName) {
        // 扫描所有可能的队列key
        String pattern = getBusinessQueueKey(taskType, modelName, "*");
        Iterable<String> keys = redissonClient.getKeys().getKeysByPattern(pattern);

        Set<String> activeBusinessIds = new HashSet<>();
        for (String key : keys) {
            RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(key);
            if (sortedSet.size() > 0) {
                // 从key中提取businessId
                String businessId = extractBusinessIdFromKey(key);
                if (businessId != null) {
                    activeBusinessIds.add(businessId);
                }
            }
        }

        // 更新活跃业务线集合
        String activeSetKey = getActiveBusinessSetKey(taskType, modelName);
        RSet<String> activeSet = redissonClient.getSet(activeSetKey);
        activeSet.clear();
        if (!activeBusinessIds.isEmpty()) {
            activeSet.addAll(activeBusinessIds);
            activeSet.expire(24, TimeUnit.HOURS);
        }

        log.info("刷新活跃业务线列表, taskType:{}, modelName:{}, activeBusinessIds:{}",
                taskType, modelName, activeBusinessIds);
    }

    /**
     * 从队列key中提取businessId
     */
    private String extractBusinessIdFromKey(String key) {
        // aigc:task:queue:{taskType}:{modelName}:{businessId}
        String[] parts = key.split(":");
        return parts.length >= 6 ? parts[5] : null;
    }

    // ==================== 兼容性方法 ====================

    /**
     * 获取排名（兼容性方法，暂时保留）
     * 注意：由于现在是多队列结构，排名计算会比较复杂
     */
    @Deprecated
    public Integer getRank(String taskType, String modelName, Long id) {
        // 这个方法在新的多队列结构下比较复杂，建议使用新的统计方法
        log.warn("getRank方法已过时，建议使用getQueueStats方法");
        return null;
    }

    /**
     * 获取排名映射（兼容性方法，暂时保留）
     */
    @Deprecated
    public Map<Long, Integer> getRankMap(String taskType, String modelName, Collection<Long> ids) {
        // 这个方法在新的多队列结构下比较复杂，建议使用新的统计方法
        log.warn("getRankMap方法已过时，建议使用getQueueStats方法");
        return Map.of();
    }
}
