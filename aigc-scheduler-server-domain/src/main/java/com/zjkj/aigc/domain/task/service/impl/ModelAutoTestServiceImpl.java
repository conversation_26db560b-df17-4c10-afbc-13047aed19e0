package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zjkj.aigc.common.constant.AigcConstant;
import com.zjkj.aigc.common.dto.ModelTestReportSubmitDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.ModelAutoTestReportStatusEnum;
import com.zjkj.aigc.common.enums.ModelAutoTestStatusEnum;
import com.zjkj.aigc.common.enums.model.ModelTestParamTypeEnum;
import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.model.test.ModelTestCreateReq;
import com.zjkj.aigc.common.req.model.test.ModelTestParamReq;
import com.zjkj.aigc.common.util.PreciseTimeNumberGenerator;
import com.zjkj.aigc.common.vo.ModelTestReportInfo;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.domain.task.service.ModelAutoTestService;
import com.zjkj.aigc.domain.task.service.impl.model.FileService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.ModelAutoTestRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.ModelAutoTestParamDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.ModelAutoTestRecordDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.ModelAutoTestReportDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestParam;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestReport;
import com.zjkj.aigc.task.dto.req.CreateAiTaskReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 模型测试服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-21 10:49:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ModelAutoTestServiceImpl implements ModelAutoTestService {

    private final ModelAutoTestParamDao modelAutoTestParamDao;
    private final ModelAutoTestRecordDao modelAutoTestRecordDao;
    private final ModelAutoTestReportDao modelAutoTestReportDao;
    private final AigcTaskService aigcTaskService;
    private final AigcTaskDao aigcTaskDao;
    private final FileService fileService;

    @Override
    public Page<ModelAutoTestRecord> queryRecordByPage(Page<ModelAutoTestRecord> page, ModelAutoTestRecordCondition condition) {
        return modelAutoTestRecordDao.queryRecordByPage(page, condition);
    }

    @Override
    public List<ModelAutoTestRecord> queryRecordList(ModelAutoTestRecordCondition condition) {
        return modelAutoTestRecordDao.queryByCondition(condition);
    }

    /**
     * 查询指定状态的相同模型
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 模型测试记录
     */
    public ModelAutoTestRecord queryAppointStatusSameModel(String taskType, String modelName) {
        List<Integer> statusList = Lists.newArrayList(ModelAutoTestStatusEnum.WAITE.getCode(), ModelAutoTestStatusEnum.RUNNING.getCode());
        ModelAutoTestRecordCondition condition = ModelAutoTestRecordCondition.builder()
                .statusList(statusList)
                .taskType(taskType)
                .modelName(modelName)
                .limit(1)
                .build();
        List<ModelAutoTestRecord> modelAutoTestRecords = modelAutoTestRecordDao.queryByCondition(condition);
        if (CollectionUtils.isEmpty(modelAutoTestRecords)) {
            return null;
        }

        return modelAutoTestRecords.get(0);
    }

    @Override
    public boolean checkSameModelExist(String taskType, String modelName) {
        ModelAutoTestRecord record = queryAppointStatusSameModel(taskType, modelName);
        return Objects.nonNull(record);
    }

    @Override
    public void saveModelAutoTest(ModelTestCreateReq req) {
        ModelAutoTestRecord modelAutoTestRecord = BeanUtil.copyProperties(req, ModelAutoTestRecord.class);
        modelAutoTestRecord.setStatus(ModelAutoTestStatusEnum.WAITE.getCode());
        String testNo = PreciseTimeNumberGenerator.generateNumber();
        modelAutoTestRecord.setTestNo(testNo);

        // 转换 modelTestParamReqList 为 List<ModelAutoTestParam>
        List<ModelTestParamReq> modelTestParamReqList = req.getModelTestParamReqList();
        List<ModelAutoTestParam> modelAutoTestParams = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(modelTestParamReqList)) {
            modelAutoTestParams = modelTestParamReqList.stream()
                    .map(paramReq -> {
                        ModelAutoTestParam param = new ModelAutoTestParam();
                        param.setTestNo(testNo);
                        param.setParamName(paramReq.getParmaName());
                        param.setParamFileUrl(paramReq.getParmaFilePath());
                        return param;
                    })
                    .collect(Collectors.toList());
        }

        String businessId = AigcConstant.BUSINESS_ID_PREFIX + testNo.substring(testNo.length() - 6);
        modelAutoTestRecord.setBusinessId(businessId);
        modelAutoTestRecordDao.save(modelAutoTestRecord);

        // 批量保存参数
        if (CollectionUtils.isNotEmpty(modelAutoTestParams)) {
            modelAutoTestParamDao.saveBatch(modelAutoTestParams);
        }
    }

    /**
     * 检查状态
     *
     * @param status 状态
     */
    public void checkStatus(Integer status, ModelAutoTestStatusEnum statusEnum) {
        if (!Objects.equals(status, statusEnum.getCode())) {
            throw new BaseBizException(CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.model.test.status.cannot.operate", new Object[]{ModelAutoTestStatusEnum.WAITE.getDesc()}));
        }

    }

    @Override
    public ModelAutoTestRecord queryByTestNo(String testNo) {
        return queryByTestNo(testNo, Boolean.FALSE);
    }

    @Override
    public List<ModelAutoTestParam> queryParamByTestNo(String testNo) {
        return modelAutoTestParamDao.queryByTestNo(testNo);
    }

    @Override
    public ModelAutoTestRecord queryByTestNo(String testNo, boolean nullThrowEx) {
        ModelAutoTestRecord record = modelAutoTestRecordDao.queryByTestNo(testNo);
        if (Objects.isNull(record) && nullThrowEx) {
            throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS);
        }

        return record;
    }

    @Override
    public boolean updateModelAutoTest(ModelTestCreateReq req) {
        ModelAutoTestRecord record = queryByTestNo(req.getTestNo(), Boolean.TRUE);
        checkStatus(record.getStatus(), ModelAutoTestStatusEnum.WAITE);

        if (!Objects.equals(req.getTaskType(), record.getTaskType()) || !Objects.equals(req.getModelName(), record.getModelName())) {
            ModelAutoTestRecord sameModelRecord = queryAppointStatusSameModel(req.getTaskType(), req.getModelName());
            if (Objects.nonNull(sameModelRecord) && !Objects.equals(record.getId(), sameModelRecord.getId())) {
                throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.test.not.allow.edit"));
            }
        }

        //将原本的动态参数删除
        modelAutoTestParamDao.removeByTestNo(req.getTestNo());

        // 转换 modelTestParamReqList 为 List<ModelAutoTestParam>
        List<ModelTestParamReq> modelTestParamReqList = req.getModelTestParamReqList();
        List<ModelAutoTestParam> modelAutoTestParams = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(modelTestParamReqList)) {
            modelAutoTestParams = modelTestParamReqList.stream()
                    .map(paramReq -> {
                        ModelAutoTestParam param = new ModelAutoTestParam();
                        param.setTestNo(req.getTestNo());
                        param.setParamName(paramReq.getParmaName());
                        param.setParamFileUrl(paramReq.getParmaFilePath());
                        return param;
                    })
                    .collect(Collectors.toList());
        }
        // 批量保存参数
        if (CollectionUtils.isNotEmpty(modelAutoTestParams)) {
            modelAutoTestParamDao.saveBatch(modelAutoTestParams);
        }

        ModelAutoTestRecord updateRecord = BeanUtil.copyProperties(req, ModelAutoTestRecord.class);
        updateRecord.setId(record.getId());
        updateRecord.setStatus(record.getStatus());

        return modelAutoTestRecordDao.updateRecord(updateRecord, updateRecord.getStatus());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startModelTest(String testNo) {
        ModelAutoTestRecord record = queryByTestNo(testNo, Boolean.TRUE);
        checkStatus(record.getStatus(), ModelAutoTestStatusEnum.WAITE);

        // 创建任务
        String businessId = record.getBusinessId();
        String taskType = record.getTaskType();
        String modelName = record.getModelName();

        // 类型检查
        ModelTestParamTypeEnum typeEnum = ModelTestParamTypeEnum.getByCode(record.getParamType());
        BaseBizException.isTrue(Objects.nonNull(typeEnum), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.type.error"));

        List<CreateAiTaskReq<Object>> taskReqs;
        if (Objects.equals(typeEnum, ModelTestParamTypeEnum.SINGLE)) {
            JSONArray array = new JSONArray(JSON.parseObject(record.getModelParams()));
            taskReqs = IntStream.range(0, record.getBatchTaskCount().intValue())
                    .boxed()
                    .flatMap(i -> array.stream().map(item -> new CreateAiTaskReq<>()
                            .setBusinessId(businessId)
                            .setTaskType(taskType)
                            .setModelName(modelName)
                            .setParams(item)))
                    .collect(Collectors.toList());
        } else {
            //从oss下载文件，然后进行解析及组装数据
            List<Map<String, Object>> paramList = queryParamValues(testNo);
            log.info("OSS中下载下来的数据为: {}",paramList);
            List<String> assembleParamList = renderTemplates(record.getModelParams(), paramList);
            log.info("填充模板之后的数据为: {}",assembleParamList);
            // 获取目标批次数量
            int targetBatchCount = record.getBatchTaskCount().intValue();
            int assembleParamSize = assembleParamList.size();

            taskReqs = IntStream.range(0, targetBatchCount)
                    .mapToObj(index -> {
                        // 通过取模运算循环使用assembleParamList中的数据
                        String item = assembleParamList.get(index % assembleParamSize);
                        JSONObject object = JSON.parseObject(item);
                        return new CreateAiTaskReq<>()
                                .setBusinessId(businessId)
                                .setTaskType(taskType)
                                .setModelName(modelName)
                                .setParams(object);
                    })
                    .collect(Collectors.toList());

            log.info("批次任务创建完成，testNo: {}, 目标批次数: {}, 参数模板数: {}, 实际生成任务数: {}",
                    testNo, targetBatchCount, assembleParamSize, taskReqs.size());
        }
        aigcTaskService.batchSaveAigcTask(taskReqs);

        // 更新状态
        ModelAutoTestRecord updateRecord = new ModelAutoTestRecord();
        updateRecord.setId(record.getId());
        updateRecord.setStatus(ModelAutoTestStatusEnum.RUNNING.getCode());
        boolean changed = modelAutoTestRecordDao.updateRecord(updateRecord, record.getStatus());
        if (!changed) {
            throw new BaseBizException(CustomErrorCode.DATA_CHANGED);
        }

    }

    private List<String> renderTemplates(String templateStr, List<Map<String, Object>> paramList) {
        VelocityEngine velocityEngine = new VelocityEngine();
        velocityEngine.init();

        List<String> renderedList = new ArrayList<>();
        for (Map<String, Object> params : paramList) {
            VelocityContext context = new VelocityContext();
            params.forEach(context::put);

            StringWriter writer = new StringWriter();
            velocityEngine.evaluate(context, writer, "Template", templateStr);
            renderedList.add(writer.toString());
        }
        return renderedList;
    }

    private List<Map<String, Object>> queryParamValues(String testNo) {
        List<Map<String, Object>> allDataList = new ArrayList<>();
        List<ModelAutoTestParam> modelAutoTestParams = modelAutoTestParamDao.queryByTestNo(testNo);

        if (CollectionUtils.isEmpty(modelAutoTestParams)) {
            log.info("未找到测试参数，testNo: {}", testNo);
            throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS);
        }

        // 存储每个文件的数据，key为参数名，value为该文件的所有行数据
        Map<String, List<String>> fileDataMap = new LinkedHashMap<>();
        int minRowCount = Integer.MAX_VALUE;

        // 1. 下载并解析所有Excel文件
        for (ModelAutoTestParam modelAutoTestParam : modelAutoTestParams) {
            String paramName = modelAutoTestParam.getParamName();
            String fileUrl = modelAutoTestParam.getParamFileUrl();

            try (InputStream inputStream = fileService.download(fileUrl)) {
                List<String> columnData = parseExcelColumn(inputStream, paramName, fileUrl);
                fileDataMap.put(paramName, columnData);
                minRowCount = Math.min(minRowCount, columnData.size());

            } catch (Exception e) {
                log.error("Excel文件处理失败，参数: {}, 文件: {}", paramName, fileUrl, e);
                throw new BaseBizException(CustomErrorCode.UNKNOWN_ERROR,
                        MessageUtils.getMessage("business.file.download.failed"));
            }
        }

        // 2. 按最小行数合并数据，确保没有空值
        for (int rowIndex = 0; rowIndex < minRowCount; rowIndex++) {
            Map<String, Object> rowDataMap = new LinkedHashMap<>();

            for (Map.Entry<String, List<String>> entry : fileDataMap.entrySet()) {
                String paramName = entry.getKey();
                List<String> columnData = entry.getValue();

                // 直接取对应行的数据，因为已经按最小行数处理，不会越界
                String cellValue = columnData.get(rowIndex);
                rowDataMap.put(paramName, cellValue);
            }

            allDataList.add(rowDataMap);
        }

        log.info("数据合并完成，testNo: {}, 文件数: {}, 最小行数: {}, 合并后数据行数: {}",
                testNo, fileDataMap.size(), minRowCount, allDataList.size());

        return allDataList;
    }

    /**
     * 解析Excel文件的单列数据
     *
     * @param inputStream 文件输入流
     * @param paramName   参数名
     * @param fileUrl     文件URL
     * @return 列数据
     */
    private List<String> parseExcelColumn(InputStream inputStream, String paramName, String fileUrl) {
        List<String> columnData = new ArrayList<>();

        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                // 每个Excel文件只有一列数据，取第一列
                String cellValue = rowData.get(0);
                columnData.add(cellValue != null ? cellValue.trim() : "");
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("Excel文件解析完成，参数: {}, 文件: {}, 行数: {}",
                        paramName, fileUrl, columnData.size());
            }
        }).sheet().doRead();

        return columnData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelModelTest(String testNo) {
        ModelAutoTestRecord record = queryByTestNo(testNo, Boolean.TRUE);
        checkStatus(record.getStatus(), ModelAutoTestStatusEnum.RUNNING);

        // 删除模型任务
        aigcTaskService.actualDelByBusinessId(record.getBusinessId());

        // 更新状态
        ModelAutoTestRecord updateRecord = new ModelAutoTestRecord();
        updateRecord.setId(record.getId());
        updateRecord.setTestCancelTime(LocalDateTime.now());
        updateRecord.setStatus(ModelAutoTestStatusEnum.CANCEL.getCode());
        boolean changed = modelAutoTestRecordDao.updateRecord(updateRecord, record.getStatus());
        if (!changed) {
            throw new BaseBizException(CustomErrorCode.DATA_CHANGED);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delModelTest(String testNo) {
        ModelAutoTestRecord record = queryByTestNo(testNo, Boolean.TRUE);
        if (Objects.equals(record.getStatus(), ModelAutoTestStatusEnum.RUNNING.getCode())) {
            throw new BaseBizException(CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.test.task.running"));
        }

        modelAutoTestRecordDao.removeById(record.getId());
        if (!Objects.equals(record.getStatus(), ModelAutoTestStatusEnum.SUCCESS.getCode())) {
            return;
        }

        // 删除测试报告
        ModelAutoTestReport report = modelAutoTestReportDao.queryByTestNo(testNo);
        if (Objects.nonNull(report)) {
            modelAutoTestReportDao.removeById(report.getId());
        }

    }

    @Override
    public ModelAutoTestReport queryReportByTestNo(String testNo) {
        return modelAutoTestReportDao.queryByTestNo(testNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportSubmit(ModelTestReportSubmitDTO<ModelTestReportInfo> reportSubmit) {
        ModelAutoTestReport report = modelAutoTestReportDao.queryByTestNo(reportSubmit.getTestNo());
        if (Objects.isNull(report)) {
           throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS);
        }

        if (Objects.equals(report.getReportStatus(), ModelAutoTestReportStatusEnum.GENERATED.getCode())) {
            throw new BaseBizException(CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.model.test.report.generated"));
        }

        ModelAutoTestRecord record = modelAutoTestRecordDao.queryByTestNo(reportSubmit.getTestNo());
        BaseBizException.isTrue(Objects.nonNull(record), CustomErrorCode.DATA_NOT_EXISTS, MessageUtils.getMessage("business.test.record.not.exists"));

        // 设置任务信息
        ModelTestReportInfo.TaskInfo taskInfo = reportSubmit.getInfo().getTaskInfo();
        taskInfo.setStartTime(LocalDateTimeUtil.formatNormal(record.getTestStartTime()));
        taskInfo.setEndTime(LocalDateTimeUtil.formatNormal(record.getTestCompletionTime()));
        ModelTestReportInfo.TaskInfo.TimeCost timeCost = taskInfo.getTimeCost();
        timeCost.setMeanMs(String.valueOf(record.getAvgElapsedTime()));
        int oneHourTaskNum = (int) (3600 * 1000 / record.getAvgElapsedTime());
        timeCost.setMeanOneHourTaskNum(String.valueOf(oneHourTaskNum));

        ModelAutoTestReport reqReport = JSON.parseObject(JSON.toJSONString(reportSubmit.getInfo()), ModelAutoTestReport.class);
        BeanUtil.copyProperties(reqReport, report, CopyOptions.create().setIgnoreNullValue(true));
        report.setGenTime(LocalDateTime.now());
        report.setServiceName(reportSubmit.getServiceName());
        report.setModelVersion(reportSubmit.getModelVersion());
        report.setReportStatus(ModelAutoTestReportStatusEnum.GENERATED.getCode());
        modelAutoTestReportDao.updateById(report);

        // 更新模型测试状态
        record.setId(record.getId());
        record.setStatus(ModelAutoTestStatusEnum.REPORTED.getCode());
        modelAutoTestRecordDao.updateRecord(record, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkDone(ModelAutoTestRecord record) {
        if (!Objects.equals(record.getStatus(), ModelAutoTestStatusEnum.RUNNING.getCode())) {
            return;
        }

        AigcTaskCondition taskCondition = AigcTaskCondition.builder()
                .businessId(record.getBusinessId())
                .build();

        List<AigcTask> aigcTasks = aigcTaskDao.queryByCondition(taskCondition);
        if (CollectionUtils.isEmpty(aigcTasks)) {
            log.info("未查询到模型任务. testNo:{}", record.getTestNo());
            return;
        }

        // 按状态分组，统计
        Map<Integer, Long> statusCountMap = aigcTasks.stream()
                .collect(Collectors.groupingBy(AigcTask::getTaskState, Collectors.counting()));

        Integer waiteState = TaskStateEnum.WAITE.getCode();
        if (statusCountMap.containsKey(waiteState) && Objects.equals(statusCountMap.get(waiteState), record.getBatchTaskCount())) {
            // 未执行
            return;
        }

        Long successTaskCount = statusCountMap.getOrDefault(TaskStateEnum.SUCC.getCode(), 0L);
        record.setSuccessTaskCount(successTaskCount);

        // 设置开始时间
        if (Objects.isNull(record.getTestStartTime())) {
            LocalDateTime minStartTime = aigcTasks.stream()
                    .filter(task -> Objects.equals(task.getTaskState(), TaskStateEnum.SUCC.getCode()))
                    .map(AigcTask::getTaskStartTime)
                    .min(LocalDateTime::compareTo)
                    .orElse(null);
            record.setTestStartTime(minStartTime);
        }

        // 执行完成
        Integer runningState = TaskStateEnum.RUNNING.getCode();
        if (!statusCountMap.containsKey(waiteState) && !statusCountMap.containsKey(runningState)) {
            LocalDateTime maxStartTime = aigcTasks.stream()
                    .filter(task -> Objects.equals(task.getTaskState(), TaskStateEnum.SUCC.getCode()))
                    .map(AigcTask::getTaskCompletionTime)
                    .max(LocalDateTime::compareTo)
                    .orElse(null);
            record.setTestCompletionTime(maxStartTime);
            ModelAutoTestStatusEnum statusEnum = successTaskCount > 0 ? ModelAutoTestStatusEnum.SUCCESS : ModelAutoTestStatusEnum.FAIL;
            record.setStatus(statusEnum.getCode());

            // 计算平均耗时
            aigcTasks.stream()
                    .filter(task -> Objects.equals(task.getTaskState(), TaskStateEnum.SUCC.getCode()))
                    .mapToLong(task -> ChronoUnit.MILLIS.between(task.getTaskStartTime(), task.getTaskCompletionTime()))
                    .average()
                    .ifPresent(avg -> record.setAvgElapsedTime((long) avg));

            // 生成测试报告
            if (Objects.equals(record.getStatus(), ModelAutoTestStatusEnum.SUCCESS.getCode())) {
                ModelAutoTestReport report = new ModelAutoTestReport();
                report.setTestNo(record.getTestNo());
                report.setTaskType(record.getTaskType());
                report.setModelName(record.getModelName());
                report.setReportStatus(ModelAutoTestReportStatusEnum.INIT.getCode());
                modelAutoTestReportDao.save(report);
            }

            if (!record.getRetainTask()) {
                // 删除模型任务
                aigcTaskService.actualDelByBusinessId(record.getBusinessId());
            }
        }

        boolean updated = modelAutoTestRecordDao.updateRecord(record, ModelAutoTestStatusEnum.RUNNING.getCode());
        if (!updated) {
            throw new BaseBizException(CustomErrorCode.DATA_CHANGED);
        }
    }
}
