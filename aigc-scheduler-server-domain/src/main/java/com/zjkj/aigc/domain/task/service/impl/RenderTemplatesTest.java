package com.zjkj.aigc.domain.task.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;

import java.io.StringWriter;
import java.util.*;

/**
 * 测试 renderTemplates 方法
 */
public class RenderTemplatesTest {

    public static void main(String[] args) {
        RenderTemplatesTest test = new RenderTemplatesTest();
        test.testRenderTemplates();
    }

    public void testRenderTemplates() {
        System.out.println("=== 测试 renderTemplates 方法 ===");
        JSONArray array = new JSONArray(JSON.parseObject("{\n" +
                "  \"text\": \"一群老鼠在游来游去，\",\n" +
                "  \"width\": \"1024\",\n" +
                "  \"height\": \"1024\",\n" +
                "  \"batch_size\": \"1\",\n" +
                "  \"workflowType\": \"xiangao_wst\"\n" +
                "}"));
        System.out.println(array);

        boolean validArray = JSON.isValidArray("[{\"taskId\": \"7341697823282308123\", \"modelType\": \"\", \"inputImage\": \"https:dcdcdcdcdc.com\", \"scalefactor\": ${scalefactor}}]");
        // 测试场景1：基本模板渲染
        testBasicTemplate();

        // 测试场景2：多参数模板
        testMultiParamTemplate();

        // 测试场景3：JSON格式模板
        testJsonTemplate();

        // 测试场景4：复杂嵌套模板
        testComplexTemplate();
    }

    /**
     * 测试场景1：基本模板渲染
     */
    private void testBasicTemplate() {
        System.out.println("\n--- 测试场景1：基本模板渲染 ---");

        String templateStr = "Hello ${name}, your age is ${age}";

        List<Map<String, Object>> paramList = new ArrayList<>();

        Map<String, Object> param1 = new HashMap<>();
        param1.put("name", "张三");
        param1.put("age", 25);
        paramList.add(param1);

        Map<String, Object> param2 = new HashMap<>();
        param2.put("name", "李四");
        param2.put("age", 30);
        paramList.add(param2);

        List<String> result = renderTemplates(templateStr, paramList);

        System.out.println("模板: " + templateStr);
        System.out.println("参数列表: " + paramList);
        System.out.println("渲染结果:");
        for (int i = 0; i < result.size(); i++) {
            System.out.println("  [" + i + "] " + result.get(i));
        }
    }

    /**
     * 测试场景2：多参数模板
     */
    private void testMultiParamTemplate() {
        System.out.println("\n--- 测试场景2：多参数模板 ---");

        String templateStr = "用户: ${username}, 邮箱: ${email}, 部门: ${department}, 角色: ${role}";

        List<Map<String, Object>> paramList = new ArrayList<>();

        Map<String, Object> param1 = new HashMap<>();
        param1.put("username", "admin");
        param1.put("email", "<EMAIL>");
        param1.put("department", "技术部");
        param1.put("role", "管理员");
        paramList.add(param1);

        Map<String, Object> param2 = new HashMap<>();
        param2.put("username", "user001");
        param2.put("email", "<EMAIL>");
        param2.put("department", "销售部");
        param2.put("role", "普通用户");
        paramList.add(param2);

        List<String> result = renderTemplates(templateStr, paramList);

        System.out.println("模板: " + templateStr);
        System.out.println("渲染结果:");
        for (int i = 0; i < result.size(); i++) {
            System.out.println("  [" + i + "] " + result.get(i));
        }
    }

    /**
     * 测试场景3：JSON格式模板
     */
    private void testJsonTemplate() {
        System.out.println("\n--- 测试场景3：JSON格式模板 ---");

        String templateStr = "{\n" +
                "    \"taskId\": \"7341697823282308123\",\n" +
                "    \"modelType\": \"\",\n" +
                "    \"inputImage\": \"${inputImage}\",\n" +
                "    \"scalefactor\": ${scalefactor}\n" +
                "  }";

        List<Map<String, Object>> paramList = new ArrayList<>();

        Map<String, Object> param1 = new HashMap<>();
        param1.put("inputImage", "https://robot-sew.oss-cn-hangzhou.aliyuncs.com/tiangong_74b85d1f901446d8a40bd8cf54945c83.png");
        param1.put("scalefactor", 4);
        paramList.add(param1);

        Map<String, Object> param2 = new HashMap<>();
        param2.put("inputImage", "https://robot-sew.oss-cn-hangzhou.aliyuncs.com/tiangong_2b7abf7349434cd6a590e2fde09b7ca9.png");
        param2.put("scalefactor", "{\n" +
                "    \"aa\": 3,\n" +
                "    \"bb\": \"bb\"\n" +
                "  }");
        paramList.add(param2);

        List<String> result = renderTemplates(templateStr, paramList);

        System.out.println("模板: " + templateStr);
        System.out.println("渲染结果:");
        System.out.println(result);
//        for (int i = 0; i < result.size(); i++) {
//            System.out.println("  [" + i + "] " + result.get(i));
//        }
    }

    /**
     * 测试场景4：复杂嵌套模板
     */
    private void testComplexTemplate() {
        System.out.println("\n--- 测试场景4：复杂嵌套模板 ---");

        String templateStr = "[{\"input\": \"${input}\", \"params\": {\"max_tokens\": ${maxTokens}, \"top_p\": ${topP}}}, {\"metadata\": {\"user_id\": \"${userId}\", \"session_id\": \"${sessionId}\"}}]";

        List<Map<String, Object>> paramList = new ArrayList<>();

        Map<String, Object> param1 = new HashMap<>();
        param1.put("input", "分析这段代码的复杂度");
        param1.put("maxTokens", 1000);
        param1.put("topP", 0.9);
        param1.put("userId", "user_123");
        param1.put("sessionId", "session_456");
        paramList.add(param1);

        Map<String, Object> param2 = new HashMap<>();
        param2.put("input", "优化这个SQL查询");
        param2.put("maxTokens", 2000);
        param2.put("topP", 0.8);
        param2.put("userId", "user_789");
        param2.put("sessionId", "session_012");
        paramList.add(param2);

        List<String> result = renderTemplates(templateStr, paramList);

        System.out.println("模板: " + templateStr);
        System.out.println("渲染结果:");
        for (int i = 0; i < result.size(); i++) {
            System.out.println("  [" + i + "] " + result.get(i));
        }
    }

    /**
     * 测试场景5：测试两种语法的区别
     */
    private void testSyntaxDifference() {
        System.out.println("\n--- 测试场景5：测试两种语法的区别 ---");

        // 测试 $variable 语法
        String template1 = "Hello $name, age: $age";

        // 测试 ${variable} 语法
        String template2 = "Hello ${name}, age: ${age}";

        // 测试混合场景（变量名后有字母）
        String template3 = "Hello ${name}abc, age: ${age}years";

        List<Map<String, Object>> paramList = new ArrayList<>();
        Map<String, Object> param = new HashMap<>();
        param.put("name", "张三");
        param.put("age", 25);
        paramList.add(param);

        System.out.println("模板1 ($variable): " + template1);
        System.out.println("结果1: " + renderTemplates(template1, paramList).get(0));

        System.out.println("模板2 (${variable}): " + template2);
        System.out.println("结果2: " + renderTemplates(template2, paramList).get(0));

        System.out.println("模板3 (${variable}混合): " + template3);
        System.out.println("结果3: " + renderTemplates(template3, paramList).get(0));
    }

    /**
     * 复制的 renderTemplates 方法用于测试
     */
    private List<String> renderTemplates(String templateStr, List<Map<String, Object>> paramList) {
        VelocityEngine velocityEngine = new VelocityEngine();
        velocityEngine.init();

        List<String> renderedList = new ArrayList<>();
        for (Map<String, Object> params : paramList) {
            VelocityContext context = new VelocityContext();
            params.forEach(context::put);

            StringWriter writer = new StringWriter();
            velocityEngine.evaluate(context, writer, "Template", templateStr);
            renderedList.add(writer.toString());
        }
        return renderedList;
    }
}
