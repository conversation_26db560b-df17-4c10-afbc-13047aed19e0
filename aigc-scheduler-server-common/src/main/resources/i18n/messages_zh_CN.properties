# CustomErrorCode\u6587\u4EF6
success=\u8BF7\u6C42\u6210\u529F\uFF01
data.not.exists=\u6570\u636E\u4E0D\u5B58\u5728
data.already.exists=\u6570\u636E\u5DF2\u5B58\u5728
data.cannot.edit=\u6570\u636E\u65E0\u6CD5\u4FEE\u6539
data.changed=\u6570\u636E\u53D1\u751F\u53D8\u5316\uFF0C\u8BF7\u91CD\u8BD5
db.data.already.exists=\u6570\u636E\u5E93\u6570\u636E\u91CD\u590D
data.error=\u6570\u636E\u5F02\u5E38
dashvector.create.collection=\u5411\u91CF\u6570\u636E\u5E93\u521B\u5EFA\u96C6\u5408\u9519\u8BEF\uFF1A{0}
dashvector.create.doc=\u5411\u91CF\u6570\u636E\u5E93\u521B\u5EFA\u6587\u6863\u9519\u8BEF\uFF1A{0}
dashvector.delete.doc=\u5411\u91CF\u6570\u636E\u5E93\u5220\u9664\u6587\u6863\u9519\u8BEF\uFF1A{0}
dashvector.query.doc=\u5411\u91CF\u6570\u636E\u5E93\u67E5\u8BE2\u6587\u6863\u9519\u8BEF\uFF1A{0}
dashvector.query.params=\u68C0\u7D22ID\u548C\u68C0\u7D22\u5411\u91CF\u6570\u7EC4\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
unauthorized=\u672A\u6388\u6743\uFF01
param.error=\u5BA2\u6237\u7AEF\u53C2\u6570\u9519\u8BEF
not.supported=NOT_SUPPORTED
unknown.error=\u672A\u77E5\u9519\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
feign.invoke=\u8FDC\u7A0B\u5916\u90E8\u670D\u52A1\u8C03\u7528\u5931\u8D25\uFF01
time.range.too.long=\u65F6\u95F4\u8DE8\u5EA6\u8D85\u8FC7{0}

# Validation related
validation.id.required=id\u4E0D\u80FD\u4E3A\u7A7A
validation.model.name.required=\u6A21\u578B\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
validation.task.type.required=\u4EFB\u52A1\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
validation.platform.required=\u5E73\u53F0\u4E0D\u80FD\u4E3A\u7A7A
validation.cloud.platform.required=\u4E91\u5E73\u53F0\u4E0D\u80FD\u4E3A\u7A7A
validation.model.id.required=\u6A21\u578Bid\u4E0D\u80FD\u4E3A\u7A7A
validation.model.version.required=\u6A21\u578B\u7248\u672C\u4E0D\u80FD\u4E3A\u7A7A
validation.status.required=\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A
validation.memory.size.required=\u5185\u5B58\u5927\u5C0F\u4E0D\u80FD\u4E3A\u7A7A
validation.cpu.core.required=cpu\u6838\u6570\u4E0D\u80FD\u4E3A\u7A7A
validation.type.required=\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
validation.name.required=\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
validation.month.required=\u6708\u4EFD\u4E0D\u80FD\u4E3A\u7A7A
validation.tag.required=\u6807\u7B7E\u4E0D\u80FD\u4E3A\u7A7A
validation.model.type.required=\u578B\u53F7\u4E0D\u80FD\u4E3A\u7A7A

# Batch operate validation
batch.operate.ids.required=\u64CD\u4F5Cid\u4E0D\u80FD\u4E3A\u7A7A

# Alarm channel validation
alarm.channel.tag.length.exceeded=\u6807\u7B7E\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
alarm.channel.type.required=\u6E20\u9053\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
alarm.channel.webhook.required=Webhook\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
alarm.channel.webhook.length.exceeded=Webhook\u5730\u5740\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
alarm.channel.sign.length.exceeded=\u7B7E\u540D\u5BC6\u94A5\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Task validation
task.params.required=\u6A21\u578B\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A

# Task batch operate validation
task.batch.operate.task.ids.required=\u5E73\u53F0\u4EFB\u52A1id\u4E0D\u80FD\u4E3A\u7A7A

# Task priority validation
task.priority.business.id.required=\u4E1A\u52A1id\u4E0D\u80FD\u4E3A\u7A7A

# Task stat summary validation
task.stat.summary.start.time.required=\u5F00\u59CB\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A
task.stat.summary.end.time.required=\u7ED3\u675F\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A

# Model deploy validation
model.deploy.title.required=\u6807\u9898\u4E0D\u80FD\u4E3A\u7A7A
model.deploy.title.length.exceeded=\u6807\u9898\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.deploy.project.name.required=\u9879\u76EE\u5DE5\u7A0B\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
model.deploy.project.name.length.exceeded=\u9879\u76EE\u5DE5\u7A0B\u540D\u79F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.deploy.project.git.url.required=\u9879\u76EEgit\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
model.deploy.project.git.url.length.exceeded=\u9879\u76EEgit\u5730\u5740\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.deploy.version.length.exceeded=\u6A21\u578B\u7248\u672C\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.deploy.sdk.version.required=\u90E8\u7F72sdk\u7248\u672C\u4E0D\u80FD\u4E3A\u7A7A
model.deploy.sdk.version.length.exceeded=\u90E8\u7F72sdk\u7248\u672C\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.deploy.oss.url.required=\u6A21\u578B\u6587\u4EF6OSS\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
model.deploy.oss.url.length.exceeded=\u6A21\u578B\u6587\u4EF6OSS\u5730\u5740\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.deploy.mirror.image.url.required=\u57FA\u7840\u955C\u50CF\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
model.deploy.mirror.image.url.length.exceeded=\u57FA\u7840\u955C\u50CF\u5730\u5740\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.deploy.resource.info.required=\u8D44\u6E90\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
model.deploy.encrypt.required=\u662F\u5426\u52A0\u5BC6\u4E0D\u80FD\u4E3A\u7A7A
model.deploy.remark.length.exceeded=\u5907\u6CE8\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.deploy.config.changed.required=\u6A21\u578B\u914D\u7F6E\u53D8\u66F4\u9879\u4E0D\u80FD\u4E3A\u7A7A

# Dict validation
dict.id.required=\u4E3B\u952E\u4E0D\u80FD\u4E3A\u7A7A
dict.name.required=\u5B57\u5178\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
dict.name.length.exceeded=\u5B57\u5178\u540D\u79F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
dict.type.required=\u5B57\u5178\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
dict.type.length.exceeded=\u5B57\u5178\u7C7B\u578B\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
dict.remark.length.exceeded=\u5907\u6CE8\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Dict data validation
dict.data.id.required=\u4E3B\u952E\u4E0D\u80FD\u4E3A\u7A7A
dict.data.dict.id.required=\u5B57\u5178\u7C7B\u578Bid\u4E0D\u80FD\u4E3A\u7A7A
dict.data.label.required=\u5B57\u5178\u6807\u7B7E\u4E0D\u80FD\u4E3A\u7A7A
dict.data.label.length.exceeded=\u5B57\u5178\u6807\u7B7E\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
dict.data.value.required=\u5B57\u5178\u952E\u503C\u4E0D\u80FD\u4E3A\u7A7A
dict.data.value.length.exceeded=\u5B57\u5178\u952E\u503C\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
dict.data.css.class.length.exceeded=\u6837\u5F0F\u5C5E\u6027\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
dict.data.color.class.length.exceeded=\u989C\u8272\u5C5E\u6027\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
dict.data.remark.length.exceeded=\u5907\u6CE8\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Model info validation
model.info.name.length.exceeded=\u540D\u79F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.info.type.length.exceeded=\u7C7B\u578B\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.info.task.type.length.exceeded=\u4EFB\u52A1\u7C7B\u578B\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.info.model.name.length.exceeded=\u6A21\u578B\u540D\u79F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.info.remark.length.exceeded=\u5907\u6CE8\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Model resource validation
model.resource.gpu.info.required=gpu\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A


# Alarm update validation
alarm.update.remark.length.exceeded=\u5904\u7406\u5907\u6CE8\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Alarm config validation
alarm.config.tag.length.exceeded=\u6807\u7B7E\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
alarm.config.model.id.required=\u5173\u8054\u6A21\u578B\u4E0D\u80FD\u4E3A\u7A7A
alarm.config.alarm.type.required=\u544A\u8B66\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
alarm.config.threshold.required=\u544A\u8B66\u9608\u503C\u914D\u7F6E\u4E0D\u80FD\u4E3A\u7A7A
alarm.config.time.interval.required=\u544A\u8B66\u65F6\u95F4\u95F4\u9694\u4E0D\u80FD\u4E3A\u7A7A
alarm.config.time.interval.unit.required=\u544A\u8B66\u65F6\u95F4\u95F4\u9694\u5355\u4F4D\u4E0D\u80FD\u4E3A\u7A7A
alarm.config.receiver.required=\u63A5\u6536\u4EBA\u914D\u7F6E\u4E0D\u80FD\u4E3A\u7A7A

# Heartbeat validation
heartbeat.status.required=\u4EFB\u52A1\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A

# Model register validation
model.register.node.ip.required=\u8282\u70B9IP\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
model.register.pod.name.required=pod\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
model.register.pod.namespace.required=pod\u547D\u540D\u7A7A\u95F4\u4E0D\u80FD\u4E3A\u7A7A

# GPU apply status validation
gpu.apply.status.remark.length.exceeded=\u5BA1\u6279\u8BF4\u660E\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Resource expend validation
resource.expend.month.required=\u652F\u51FA\u6708\u4EFD\u4E0D\u80FD\u4E3A\u7A7A
resource.expend.list.required=\u652F\u51FA\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A
resource.expend.remark.length.exceeded=\u5907\u6CE8\u8BF4\u660E\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
resource.expend.budget.diff.remark.length.exceeded=\u9884\u7B97\u5DEE\u5F02\u8BF4\u660E\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Resource bill validation
resource.bill.redundancy.mid.price.required=\u4E2D\u95F4\u4EF6\u8D39\u7528\u5197\u4F59\u6BD4\u4F8B\u4E0D\u80FD\u4E3A\u7A7A
resource.bill.redundancy.gpu.price.required=GPU\u8D39\u7528\u5197\u4F59\u6BD4\u4F8B\u4E0D\u80FD\u4E3A\u7A7A

# Resource mid plan validation
resource.mid.plan.price.remark.length.exceeded=\u4EF7\u683C\u8BF4\u660E\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
resource.mid.plan.remark.length.exceeded=\u5907\u6CE8\u8BF4\u660E\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Resource bill group config validation
resource.bill.group.config.redundancy.list.required=\u5197\u4F59\u6BD4\u4F8B\u96C6\u5408\u4E0D\u80FD\u4E3A\u7A7A
resource.bill.group.config.mid.price.redundancy.required=\u4E2D\u95F4\u4EF6\u8D39\u7528\u5197\u4F59\u6BD4\u4F8B\u4E0D\u80FD\u4E3A\u7A7A
resource.bill.group.config.gpu.price.redundancy.required=GPU\u8D39\u7528\u5197\u4F59\u6BD4\u4F8B\u4E0D\u80FD\u4E3A\u7A7A

# Resource gpu plan validation
resource.gpu.plan.model.length.exceeded=\u578B\u53F7\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
resource.gpu.plan.remark.length.exceeded=\u5907\u6CE8\u8BF4\u660E\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Resource plan validation
resource.plan.month.required=\u89C4\u5212\u6708\u4EFD\u4E0D\u80FD\u4E3A\u7A7A
resource.plan.platform.length.exceeded=\u4E91\u5E73\u53F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Resource plan query validation
resource.plan.query.month.required=\u89C4\u5212\u6708\u4EFD\u4E0D\u80FD\u4E3A\u7A7A

# Resource expend query validation
resource.expend.query.month.required=\u652F\u51FA\u6708\u4EFD\u4E0D\u80FD\u4E3A\u7A7A

# GPU apply validation
gpu.apply.type.required=\u5BA1\u6279\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
gpu.apply.type.length.exceeded=\u5BA1\u6279\u7C7B\u578B\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
gpu.apply.cluster.business.required=\u96C6\u7FA4/\u4E1A\u52A1\u4E0D\u80FD\u4E3A\u7A7A
gpu.apply.cluster.business.length.exceeded=\u96C6\u7FA4/\u4E1A\u52A1\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
gpu.apply.platform.length.exceeded=\u4E91\u5E73\u53F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
gpu.apply.gpu.model.required=GPU\u578B\u53F7\u4E0D\u80FD\u4E3A\u7A7A
gpu.apply.gpu.model.length.exceeded=GPU\u578B\u53F7\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
gpu.apply.gpu.num.required=GPU\u5361\u6570\u4E0D\u80FD\u4E3A\u7A7A
gpu.apply.remark.length.exceeded=\u8865\u5145\u8BF4\u660E\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
gpu.apply.supply.remark.length.exceeded=\u5BA1\u6279\u8BF4\u660E\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Cluster validation
cluster.platform.length.exceeded=\u5E73\u53F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
cluster.name.length.exceeded=\u540D\u79F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
cluster.service.type.required=\u670D\u52A1\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
cluster.nodes.required=\u8282\u70B9\u5217\u8868\u4E0D\u80FD\u4E3A\u7A7A

# Cluster node validation
cluster.node.instance.id.length.exceeded=\u5B9E\u4F8Bid\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
cluster.node.ip.required=\u8282\u70B9IP\u4E0D\u80FD\u4E3A\u7A7A
cluster.node.ip.length.exceeded=\u8282\u70B9IP\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
cluster.node.cost.required=\u6210\u672C\u4E0D\u80FD\u4E3A\u7A7A
cluster.node.cost.min.value=\u6210\u672C\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E{value}
cluster.node.gpu.id.required=GPU ID\u4E0D\u80FD\u4E3A\u7A7A

# Cluster usage validation
cluster.usage.cluster.id.required=\u96C6\u7FA4id\u4E0D\u80FD\u4E3A\u7A7A
cluster.usage.date.required=\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
cluster.usage.total.gpu.memory.required=\u603Bgpu\u663E\u5B58\u5927\u5C0F\u4E0D\u80FD\u4E3A\u7A7A
cluster.usage.used.gpu.memory.required=\u5DF2\u4F7F\u7528gpu\u663E\u5B58\u5927\u5C0F\u4E0D\u80FD\u4E3A\u7A7A
cluster.usage.gpu.usage.rate.required=gpu\u4F7F\u7528\u7387\u4E0D\u80FD\u4E3A\u7A7A
cluster.usage.gpu.usage.rate.min=gpu\u4F7F\u7528\u7387\u4E0D\u80FD\u5C0F\u4E8E{value}
cluster.usage.gpu.usage.rate.max=gpu\u4F7F\u7528\u7387\u4E0D\u80FD\u5927\u4E8E{value}
cluster.usage.remark.length.exceeded=\u5907\u6CE8\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Cluster model type usage validation
cluster.model.type.usage.start.date.required=\u5F00\u59CB\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
cluster.model.type.usage.end.date.required=\u7ED3\u675F\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A

# Cluster usage stat validation
cluster.usage.stat.data.date.required=\u6570\u636E\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A

# Cluster model usage validation
cluster.model.usage.cluster.id.required=\u96C6\u7FA4id\u4E0D\u80FD\u4E3A\u7A7A
cluster.model.usage.data.date.required=\u6570\u636E\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A

# Dynamic config validation
dynamic.config.min.replica.required=\u6700\u5C0F\u526F\u672C\u6570\u4E0D\u80FD\u4E3A\u7A7A
dynamic.config.min.replica.min.value=\u6700\u5C0F\u526F\u672C\u6570\u6700\u5C0F\u503C\u4E3A{value}
dynamic.config.max.replica.required=\u6700\u5927\u526F\u672C\u6570\u4E0D\u80FD\u4E3A\u7A7A
dynamic.config.max.replica.max.value=\u6700\u5927\u526F\u672C\u6570\u4E0D\u80FD\u8D85\u8FC7{value}
dynamic.config.cooldown.minute.min.value=\u51B7\u5374\u5206\u949F\u6570\u6700\u5C0F\u503C\u4E3A{value}
dynamic.config.scale.out.minute.required=\u6269\u5BB9\u7EF4\u5EA6\u4E0D\u80FD\u4E3A\u7A7A
dynamic.config.scale.out.minute.min.value=\u6269\u5BB9\u7EF4\u5EA6\u6700\u5C0F\u503C\u4E3A{value}
dynamic.config.scale.out.threshold.required=\u6269\u5BB9\u8D1F\u8F7D\u4E0D\u80FD\u4E3A\u7A7A
dynamic.config.scale.out.threshold.range=\u6269\u5BB9\u8D1F\u8F7D\u8303\u56F4\u57281-100%\u4E4B\u95F4
dynamic.config.scale.in.minute.required=\u7F29\u5BB9\u7EF4\u5EA6\u4E0D\u80FD\u4E3A\u7A7A
dynamic.config.scale.in.minute.min.value=\u7F29\u5BB9\u7EF4\u5EA6\u6700\u5C0F\u503C\u4E3A{value}
dynamic.config.scale.in.threshold.required=\u7F29\u5BB9\u8D1F\u8F7D\u4E0D\u80FD\u4E3A\u7A7A
dynamic.config.scale.in.threshold.range=\u7F29\u5BB9\u8D1F\u8F7D\u8303\u56F4\u57281-100%\u4E4B\u95F4

# Dynamic config global validation
dynamic.config.global.zadig.endpoint.length.exceeded=Zadig endpoint\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
dynamic.config.global.zadig.api.token.length.exceeded=Zadig api token\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# Host validation
host.ip.required=\u4E3B\u673Aip\u4E0D\u80FD\u4E3A\u7A7A
host.ip.length.exceeded=\u4E3B\u673Aip\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
host.platform.length.exceeded=\u5E73\u53F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
host.region.required=\u533A\u57DF\u4E0D\u80FD\u4E3A\u7A7A
host.region.length.exceeded=\u533A\u57DF\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
host.remark.length.exceeded=\u5907\u6CE8\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26

# GPU validation
gpu.card.count.required=\u5361\u6570\u4E0D\u80FD\u4E3A\u7A7A
gpu.card.count.min.value=\u5361\u6570\u4E0D\u80FD\u5C0F\u4E8E{value}
gpu.single.card.memory.required=\u5355\u5361\u663E\u5B58\u4E0D\u80FD\u4E3A\u7A7A
gpu.single.card.memory.min.value=\u5355\u5361\u663E\u5B58\u4E0D\u80FD\u5C0F\u4E8E{value}

# Model config validation
model.config.project.name.required=\u9879\u76EE\u5DE5\u7A0B\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
model.config.project.name.length.exceeded=\u9879\u76EE\u5DE5\u7A0B\u540D\u79F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.config.project.git.url.required=\u9879\u76EEgit\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
model.config.project.git.url.length.exceeded=\u9879\u76EEgit\u5730\u5740\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.config.version.length.exceeded=\u6A21\u578B\u7248\u672C\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.config.sdk.version.required=\u90E8\u7F72sdk\u7248\u672C\u4E0D\u80FD\u4E3A\u7A7A
model.config.sdk.version.length.exceeded=\u90E8\u7F72sdk\u7248\u672C\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.config.oss.url.required=\u6A21\u578B\u6587\u4EF6OSS\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
model.config.oss.url.length.exceeded=\u6A21\u578B\u6587\u4EF6OSS\u5730\u5740\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.config.encrypt.required=\u662F\u5426\u52A0\u5BC6\u4E0D\u80FD\u4E3A\u7A7A
model.config.mirror.image.url.required=\u57FA\u7840\u955C\u50CF\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
model.config.mirror.image.url.length.exceeded=\u57FA\u7840\u955C\u50CF\u5730\u5740\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.config.memory.size.required=\u5185\u5B58\u5927\u5C0FGB\u4E0D\u80FD\u4E3A\u7A7A
model.config.gpu.info.required=gpu\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A

# Model instance adjust validation
model.instance.adjust.model.info.required=\u8C03\u6574\u6A21\u578B\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A

# Task model callback validation
task.model.callback.task.id.required=\u4EFB\u52A1ID\u4E0D\u80FD\u4E3A\u7A7A

# Model test validation
model.test.name.length.exceeded=\u540D\u79F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.test.task.type.length.exceeded=\u4EFB\u52A1\u7C7B\u578B\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.test.model.name.length.exceeded=\u6A21\u578B\u540D\u79F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.test.param.type.required=\u53C2\u6570\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
model.test.param.type.length.exceeded=\u53C2\u6570\u7C7B\u578B\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7{max}\u4E2A\u5B57\u7B26
model.test.model.params.required=\u6A21\u578B\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
model.test.batch.task.count.required=\u4EFB\u52A1\u6279\u6B21\u6570\u4E0D\u80FD\u4E3A\u7A7A
model.test.batch.task.count.min.value=\u4EFB\u52A1\u6279\u6B21\u6570\u4E0D\u80FD\u5C0F\u4E8E{value}
model.test.batch.task.count.max.value=\u4EFB\u52A1\u6279\u6B21\u6570\u4E0D\u80FD\u8D85\u8FC7{value}

# Model test report validation
model.test.report.test.no.required=\u6D4B\u8BD5\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A
model.test.report.service.name.required=\u670D\u52A1\u540D\u4E0D\u80FD\u4E3A\u7A7A
model.test.report.info.required=\u62A5\u544A\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A

# Model encrypt callback validation
model.encrypt.callback.deploy.id.required=\u90E8\u7F72\u53D1\u5E03id\u4E0D\u80FD\u4E3A\u7A7A

# Model heartbeat validation
model.heartbeat.register.id.required=\u6CE8\u518Cid\u4E0D\u80FD\u4E3A\u7A7A


# Node validation
node.heartbeat.id.required=\u6CE8\u518Cid\u4E0D\u80FD\u4E3A\u7A7A
node.register.node.ip.required=\u8282\u70B9IP\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
node.register.hostname.required=\u4E3B\u673A\u540D\u4E0D\u80FD\u4E3A\u7A7A

validation.task.ids.empty=\u4EFB\u52A1id\u96C6\u5408\u4E0D\u80FD\u4E3A\u7A7A
validation.params.null=\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
validation.task.batch.range=\u4EFB\u52A1\u6279\u91CF\u6807\u8BC6\u5FC5\u987B\u662F0\u62161

# Business error messages
business.sort.field.not.exists=\u6392\u5E8F\u5B57\u6BB5[{0}]\u4E0D\u5B58\u5728
business.model.param.type.error=\u7C7B\u578B\u9519\u8BEF
business.model.param.format.error.object=\u6A21\u578B\u53C2\u6570\u683C\u5F0F\u9519\u8BEF, \u9700\u4E3A{}
business.model.param.format.error.array=\u6A21\u578B\u53C2\u6570\u683C\u5F0F\u9519\u8BEF, \u9700\u4E3A[{},{}]
business.file.upload.empty=\u4E0A\u4F20\u6587\u4EF6\u7684\u4E0D\u80FD\u4E3A\u7A7A
business.file.name.empty=\u6587\u4EF6\u540D\u4E0D\u80FD\u4E3A\u7A7A
business.file.format.unsupported=\u4EC5\u652F\u6301\u6587\u4EF6\u683C\u5F0F{0}
business.file.upload.size.exceeded=\u4E0A\u4F20\u6587\u4EF6\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC7{0}
business.file.upload.failed=\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25
business.file.download.failed=\u6587\u4EF6\u4E0B\u8F7D\u5931\u8D25
business.file.not.found=\u6587\u4EF6\u4E0D\u5B58\u5728
business.file.path.empty=\u6587\u4EF6\u8DEF\u5F84\u4E0D\u80FD\u4E3A\u7A7A
business.platform.unsupported=\u4E0D\u652F\u6301\u7684\u4E91\u5E73\u53F0[{0}]
business.file.content.column.empty=r[{0}]\u6587\u4EF6\u5185\u5BB9[{1}]\u5217\u4E0D\u80FD\u4E3A\u7A7A
business.file.date.format.error=r[{0}]{1}\u683C\u5F0F\u4E3Ayyyy-MM\uFF0C\u5982\uFF1A2025-06
business.bill.type.unsupported=\u4E0D\u652F\u6301\u7684\u8D26\u5355\u7C7B\u578B[{0}]
business.bill.import.data.empty=\u5BFC\u5165\u8D26\u5355\u7C7B\u578B/\u5217\u8868/\u4E91\u5E73\u53F0\u4E3A\u7A7A
business.bill.no.matching.data=\u65E0\u5339\u914D\u7684\u8D26\u5355\u6570\u636E\uFF0C\u8BF7\u68C0\u67E5\u8D26\u5355\u6570\u636E
business.task.info.empty=\u4EFB\u52A1\u4FE1\u606F\u4E3A\u7A7A
business.task.create.failed=\u4EFB\u52A1\u521B\u5EFA\u5931\u8D25
business.task.model.name.ids.empty=modelName/taskIds\u4E0D\u80FD\u4E3A\u7A7A
business.task.id.model.name.empty=taskId/modelName\u4E0D\u80FD\u4E3A\u7A7A
business.task.id.empty=taskId \u4E0D\u80FD\u4E3A\u7A7A
business.compare.type.empty=\u6BD4\u8F83\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
business.task.type.model.name.empty=\u4EFB\u52A1\u7C7B\u578B/\u6A21\u578B\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
business.model.param.format.error=\u6A21\u578B\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
business.model.param.empty=\u6A21\u578B\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
business.dict.type.exists=\u8BE5\u5B57\u5178\u7C7B\u578B\u5DF2\u5B58\u5728\uFF0C\u65E0\u6CD5\u66F4\u65B0
business.model.test.status.cannot.operate=\u975E\u3010{0}\u3011\u72B6\u6001\u65E0\u6CD5\u64CD\u4F5C\uFF0C\u8BF7\u5237\u65B0\u540E\u91CD\u8BD5
business.model.test.report.generated=\u6D4B\u8BD5\u62A5\u544A\u5DF2\u751F\u6210\uFF0C\u8BF7\u52FF\u91CD\u590D\u63D0\u4EA4
business.dynamic.adjust.status.cannot.cancel=\u52A8\u6001\u8C03\u6574\u8BB0\u5F55\u72B6\u6001\u4E0D\u652F\u6301\u53D6\u6D88
business.task.ids.business.id.empty=taskIds/businessId \u4E0D\u80FD\u4E3A\u7A7A
business.batch.task.count.limit=\u6279\u91CF\u4EFB\u52A1\u6570\u4E0D\u80FD\u8D85\u8FC7200
business.bill.period.instance.duplicate=\u5E10\u671F[{0}]\u5B9E\u4F8Bid[{1}]\u91CD\u590D\uFF0C\u8BF7\u68C0\u67E5\u8D26\u5355\u6570\u636E
business.dict.type.already.exists=\u8BE5\u5B57\u5178\u7C7B\u578B\u5DF2\u5B58\u5728
business.model.test.already.exists=\u8BE5\u6A21\u578B\u6D4B\u8BD5\u5DF2\u5B58\u5728\uFF0C\u8BF7\u5148\u6267\u884C\u6216\u7B49\u5F85\u6267\u884C\u5B8C\u6210
business.compare.type.unsupported=\u6BD4\u8F83\u7C7B\u578B\u4E0D\u652F\u6301
business.resource.expend.month.data.exists=\u5F53\u524D\u6708\u4EFD\u652F\u51FA\u6570\u636E\u5DF2\u5B58\u5728
business.node.ip.duplicate=\u8282\u70B9IP\u91CD\u590D:{0}
business.deploy.status.only.deploying=\u53EA\u5141\u8BB8\u53D1\u5E03\u4E2D\u72B6\u6001\u7684\u53D1\u5E03\u8BB0\u5F55
business.model.already.exists=\u8BE5\u6A21\u578B\u5DF2\u5B58\u5728
business.model.not.supported.contact.admin=\u4E0D\u652F\u6301\u7684\u6A21\u578B\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
business.service.not.configured.contact.admin=\u670D\u52A1\u672A\u914D\u7F6E\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
business.request.key.mismatch=\u8BF7\u6C42key\u4E0D\u5339\u914D
business.cluster.disable.before.delete=\u8BF7\u5148\u7981\u7528\u96C6\u7FA4,\u518D\u5220\u9664
business.approval.completed=\u5BA1\u6279\u5DF2\u5B8C\u6210\uFF0C\u65E0\u6CD5\u518D\u6B21\u6267\u884C\u8BE5\u64CD\u4F5C
business.upload.file.content.empty=\u4E0A\u4F20\u6587\u4EF6\u7684\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A
business.service.type.not.match=\u3010{0}\u3011\u96C6\u7FA4\u6807\u8BB0\u7684\u670D\u52A1\u7C7B\u578B\u3010{1}\u3011\u4E0E\u3010{2}\u3011\u6A21\u578B\u6CE8\u518C\u4FE1\u606F\u7C7B\u578B\u3010{3}\u3011\u4E0D\u4E00\u81F4
business.current.month.plan.data.exists=\u5F53\u524D\u6708\u4EFD\u89C4\u5212\u6570\u636E\u5DF2\u5B58\u5728\uFF0C\u8BF7\u5148\u5220\u9664\u518D\u6DFB\u52A0
business.task.not.exists=\u4EFB\u52A1\u4E0D\u5B58\u5728
business.param.together.empty=taskIds&taskType&modelName \u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
business.notify.model.encrypt.failed=\u901A\u77E5\u6A21\u578B\u52A0\u5BC6\u5931\u8D25
business.not.encrypting.status=\u975E\u3010\u52A0\u5BC6\u4E2D\u3011\u72B6\u6001\u7684\u53D1\u5E03\u8BB0\u5F55
business.task.cancelled=\u4EFB\u52A1\u5DF2\u53D6\u6D88\uFF0C\u65E0\u6CD5\u91CD\u590D\u53D6\u6D88
business.task.status.not.allow.cancel=\u4EFB\u52A1\u72B6\u6001\u4E0D\u5141\u8BB8\u53D6\u6D88
business.model.test.not.allow.edit=\u8BE5\u6A21\u578B\u6D4B\u8BD5\u5DF2\u5B58\u5728\uFF0C\u65E0\u6CD5\u4FEE\u6539
business.test.task.running=\u6D4B\u8BD5\u4EFB\u52A1\u6B63\u5728\u6267\u884C\u4E2D\uFF0C\u8BF7\u5148\u53D6\u6D88\u540E\u5220\u9664
business.not.allow.retry=\u65E0\u7B26\u5408\u91CD\u8BD5\u7684\u4EFB\u52A1
business.retry.result=\u91CD\u8BD5\u6210\u529F\uFF1A{0}\uFF0C\u91CD\u8BD5\u5931\u8D25\uFF1A{1}
business.not.allow.cancel=\u65E0\u7B26\u5408\u53D6\u6D88\u7684\u4EFB\u52A1
business.model.deploy.already.exists=\u8BE5\u6A21\u578B\u53D1\u5E03\u5DF2\u5B58\u5728\uFF0C\u8BF7\u5148\u53D1\u5E03\u6216\u7B49\u5F85\u53D1\u5E03\u5B8C\u6210
business.model.not.exists=\u8BE5\u6A21\u578B\u4E0D\u5B58\u5728
business.current.status.cannot.rollback=\u5F53\u524D\u72B6\u6001\u65E0\u6CD5\u56DE\u9000/\u6570\u636E\u53D1\u751F\u53D8\u5316\uFF0C\u8BF7\u91CD\u8BD5
business.cluster.date.record.exists=\u8BE5\u96C6\u7FA4\u5728\u6B64\u65E5\u671F\u5DF2\u6709\u8BB0\u5F55
business.cluster.date.record.exists.cannot.update=\u8BE5\u96C6\u7FA4\u5728\u6B64\u65E5\u671F\u5DF2\u6709\u8BB0\u5F55\uFF0C\u65E0\u6CD5\u66F4\u65B0
business.gpu.in.use.cannot.delete=GPU\u4F7F\u7528\u4E2D\uFF0C\u65E0\u6CD5\u5220\u9664
business.host.ip.already.exists=\u4E3B\u673AIP\u5DF2\u5B58\u5728
business.host.enabled.cannot.delete=\u542F\u7528\u72B6\u6001\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664\uFF0C\u8BF7\u5148\u7981\u7528
business.deploy.draft.only.edit=\u53EA\u80FD\u4FEE\u6539\u8349\u7A3F\u72B6\u6001\u7684\u53D1\u5E03\u8BB0\u5F55
business.deploy.deploying.cannot.delete=\u53D1\u5E03\u4E2D\u7684\u8BB0\u5F55\u65E0\u6CD5\u5220\u9664
business.deploy.draft.only.request=\u53EA\u80FD\u7533\u8BF7\u53D1\u5E03\u8349\u7A3F\u72B6\u6001\u7684\u53D1\u5E03\u8BB0\u5F55
business.model.type.error=\u7C7B\u578B\u9519\u8BEF
business.test.record.not.exists=\u6D4B\u8BD5\u8BB0\u5F55\u4E0D\u5B58\u5728
business.model.config.not.exists=\u8BE5\u6A21\u578B\u914D\u7F6E\u4E0D\u5B58\u5728
business.model.config.already.exists=\u6A21\u578B\u914D\u7F6E\u5DF2\u5B58\u5728
business.no.cluster.nodes=\u65E0\u96C6\u7FA4\u8282\u70B9\uFF0C\u8BF7\u68C0\u67E5\u96C6\u7FA4\u914D\u7F6E

