# CustomErrorCode\u6587\u4EF6
success=Request successful
data.not.exists=Data does not exist
data.already.exists=Data already exists
data.cannot.edit=Data cannot be modified
data.changed=Data has changed, please retry
db.data.already.exists=Database data duplication
data.error=Data error
dashvector.create.collection=%s
dashvector.create.doc=%s
dashvector.delete.doc=%s
dashvector.query.doc=%s
dashvector.query.params=Search ID and search vector array cannot be empty at the same time
unauthorized=Unauthorized!
param.error=Client parameter error
not.supported=NOT_SUPPORTED
unknown.error=Unknown error, please contact administrator
feign.invoke=Remote external service call failed!
time.range.too.long=Time range exceeds %s

# Validation related
validation.id.required=ID cannot be empty
validation.model.name.required=Model name cannot be empty
validation.task.type.required=Task type cannot be empty
validation.platform.required=Platform cannot be empty
validation.cloud.platform.required=Cloud platform cannot be empty
validation.model.id.required=Model ID cannot be empty
validation.model.version.required=Model version cannot be empty
validation.status.required=Status cannot be empty
validation.memory.size.required=Memory size cannot be empty
validation.cpu.core.required=CPU core count cannot be empty
validation.type.required=Type cannot be empty
validation.name.required=Name cannot be empty
validation.month.required=Month cannot be empty
validation.tag.required=Tag cannot be empty
validation.model.type.required=Model cannot be empty

# Batch operate validation
batch.operate.ids.required=Operation IDs cannot be empty

# Alarm channel validation
alarm.channel.tag.length.exceeded=Tag cannot exceed {max} characters
alarm.channel.type.required=Channel type cannot be empty
alarm.channel.webhook.required=Webhook URL cannot be empty
alarm.channel.webhook.length.exceeded=Webhook URL cannot exceed {max} characters
alarm.channel.sign.length.exceeded=Signature key cannot exceed {max} characters

# Task validation
task.params.required=Model parameters cannot be empty

# Task batch operate validation
task.batch.operate.task.ids.required=Platform task IDs cannot be empty

# Task priority validation
task.priority.business.id.required=Business ID cannot be empty

# Task stat summary validation
task.stat.summary.start.time.required=Start time cannot be empty
task.stat.summary.end.time.required=End time cannot be empty

# Model deploy validation
model.deploy.title.required=Title cannot be empty
model.deploy.title.length.exceeded=Title length cannot exceed {max} characters
model.deploy.project.name.required=Project name cannot be empty
model.deploy.project.name.length.exceeded=Project name length cannot exceed {max} characters
model.deploy.project.git.url.required=Project git URL cannot be empty
model.deploy.project.git.url.length.exceeded=Project git URL length cannot exceed {max} characters
model.deploy.version.length.exceeded=Model version length cannot exceed {max} characters
model.deploy.sdk.version.required=Deploy SDK version cannot be empty
model.deploy.sdk.version.length.exceeded=Deploy SDK version length cannot exceed {max} characters
model.deploy.oss.url.required=Model file OSS URL cannot be empty
model.deploy.oss.url.length.exceeded=Model file OSS URL length cannot exceed {max} characters
model.deploy.mirror.image.url.required=Base mirror image URL cannot be empty
model.deploy.mirror.image.url.length.exceeded=Base mirror image URL length cannot exceed {max} characters
model.deploy.resource.info.required=Resource information cannot be empty
model.deploy.encrypt.required=Encryption flag cannot be empty
model.deploy.remark.length.exceeded=Remark length cannot exceed {max} characters
model.deploy.config.changed.required=Model configuration changes cannot be empty

# Dict validation
dict.id.required=Primary key cannot be empty
dict.name.required=Dictionary name cannot be empty
dict.name.length.exceeded=Dictionary name length cannot exceed {max} characters
dict.type.required=Dictionary type cannot be empty
dict.type.length.exceeded=Dictionary type length cannot exceed {max} characters
dict.remark.length.exceeded=Remark length cannot exceed {max} characters

# Dict data validation
dict.data.id.required=Primary key cannot be empty
dict.data.dict.id.required=Dictionary type ID cannot be empty
dict.data.label.required=Dictionary label cannot be empty
dict.data.label.length.exceeded=Dictionary label length cannot exceed {max} characters
dict.data.value.required=Dictionary value cannot be empty
dict.data.value.length.exceeded=Dictionary value length cannot exceed {max} characters
dict.data.css.class.length.exceeded=CSS class length cannot exceed {max} characters
dict.data.color.class.length.exceeded=Color class length cannot exceed {max} characters
dict.data.remark.length.exceeded=Remark length cannot exceed {max} characters

# Model info validation
model.info.name.length.exceeded=Name length cannot exceed {max} characters
model.info.type.length.exceeded=Type length cannot exceed {max} characters
model.info.task.type.length.exceeded=Task type length cannot exceed {max} characters
model.info.model.name.length.exceeded=Model name length cannot exceed {max} characters
model.info.remark.length.exceeded=Remark length cannot exceed {max} characters

# Model resource validation
model.resource.gpu.info.required=GPU information cannot be empty

# GPU info validation
gpu.info.model.required=Model cannot be empty

# Alarm update validation
alarm.update.remark.length.exceeded=Processing remark length cannot exceed {max} characters


# Alarm config validation
alarm.config.tag.length.exceeded=Tag length cannot exceed {max} characters
alarm.config.model.id.required=Associated model cannot be empty
alarm.config.alarm.type.required=Alarm type cannot be empty
alarm.config.threshold.required=Alarm threshold configuration cannot be empty
alarm.config.time.interval.required=Alarm time interval cannot be empty
alarm.config.time.interval.unit.required=Alarm time interval unit cannot be empty
alarm.config.receiver.required=Receiver configuration cannot be empty

# Heartbeat validation
heartbeat.status.required=Task status cannot be empty

# Model register validation
model.register.node.ip.required=Node IP address cannot be empty
model.register.pod.name.required=Pod name cannot be empty
model.register.pod.namespace.required=Pod namespace cannot be empty

# GPU apply status validation
gpu.apply.status.remark.length.exceeded=Approval remark length cannot exceed {max} characters

# Resource expend validation
resource.expend.month.required=Expenditure month cannot be empty
resource.expend.list.required=Expenditure data cannot be empty
resource.expend.remark.length.exceeded=Remark length cannot exceed {max} characters
resource.expend.budget.diff.remark.length.exceeded=Budget difference remark length cannot exceed {max} characters

# Resource bill validation
resource.bill.redundancy.mid.price.required=Middleware cost redundancy ratio cannot be empty
resource.bill.redundancy.gpu.price.required=GPU cost redundancy ratio cannot be empty

# Resource mid plan validation
resource.mid.plan.price.remark.length.exceeded=Price remark length cannot exceed {max} characters
resource.mid.plan.remark.length.exceeded=Remark length cannot exceed {max} characters

# Resource bill group config validation
resource.bill.group.config.redundancy.list.required=Redundancy ratio list cannot be empty
resource.bill.group.config.mid.price.redundancy.required=Middleware cost redundancy ratio cannot be empty
resource.bill.group.config.gpu.price.redundancy.required=GPU cost redundancy ratio cannot be empty

# Resource gpu plan validation
resource.gpu.plan.model.length.exceeded=Model length cannot exceed {max} characters
resource.gpu.plan.remark.length.exceeded=Remark length cannot exceed {max} characters

# Resource plan validation
resource.plan.month.required=Planning month cannot be empty
resource.plan.platform.length.exceeded=Cloud platform length cannot exceed {max} characters

# Resource plan query validation
resource.plan.query.month.required=Planning month cannot be empty

# Resource expend query validation
resource.expend.query.month.required=Expenditure month cannot be empty

# GPU apply validation
gpu.apply.type.required=Approval type cannot be empty
gpu.apply.type.length.exceeded=Approval type length cannot exceed {max} characters
gpu.apply.cluster.business.required=Cluster/Business cannot be empty
gpu.apply.cluster.business.length.exceeded=Cluster/Business length cannot exceed {max} characters
gpu.apply.platform.length.exceeded=Cloud platform length cannot exceed {max} characters
gpu.apply.gpu.model.required=GPU model cannot be empty
gpu.apply.gpu.model.length.exceeded=GPU model length cannot exceed {max} characters
gpu.apply.gpu.num.required=GPU card count cannot be empty
gpu.apply.remark.length.exceeded=Additional remark length cannot exceed {max} characters
gpu.apply.supply.remark.length.exceeded=Approval remark length cannot exceed {max} characters

# Cluster validation
cluster.platform.length.exceeded=Platform length cannot exceed {max} characters
cluster.name.length.exceeded=Name length cannot exceed {max} characters
cluster.service.type.required=Service type cannot be empty
cluster.nodes.required=Node list cannot be empty

# Cluster node validation
cluster.node.instance.id.length.exceeded=Instance ID length cannot exceed {max} characters
cluster.node.ip.required=Node IP cannot be empty
cluster.node.ip.length.exceeded=Node IP length cannot exceed {max} characters
cluster.node.cost.required=Cost cannot be empty
cluster.node.cost.min.value=Cost must be greater than or equal to {value}
cluster.node.gpu.id.required=GPU ID cannot be empty

# Cluster usage validation
cluster.usage.cluster.id.required=Cluster ID cannot be empty
cluster.usage.date.required=Date cannot be empty
cluster.usage.total.gpu.memory.required=Total GPU memory size cannot be empty
cluster.usage.used.gpu.memory.required=Used GPU memory size cannot be empty
cluster.usage.gpu.usage.rate.required=GPU usage rate cannot be empty
cluster.usage.gpu.usage.rate.min=GPU usage rate cannot be less than {value}
cluster.usage.gpu.usage.rate.max=GPU usage rate cannot be greater than {value}
cluster.usage.remark.length.exceeded=Remark length cannot exceed {max} characters

# Cluster model type usage validation
cluster.model.type.usage.start.date.required=Start date cannot be empty
cluster.model.type.usage.end.date.required=End date cannot be empty

# Cluster usage stat validation
cluster.usage.stat.data.date.required=Data date cannot be empty

# Cluster model usage validation
cluster.model.usage.cluster.id.required=Cluster ID cannot be empty
cluster.model.usage.data.date.required=Data date cannot be empty

# Dynamic config validation
dynamic.config.min.replica.required=Minimum replica count cannot be empty
dynamic.config.min.replica.min.value=Minimum replica count minimum value is {value}
dynamic.config.max.replica.required=Maximum replica count cannot be empty
dynamic.config.max.replica.max.value=Maximum replica count cannot exceed {value}
dynamic.config.cooldown.minute.min.value=Cooldown minutes minimum value is {value}
dynamic.config.scale.out.minute.required=Scale out dimension cannot be empty
dynamic.config.scale.out.minute.min.value=Scale out dimension minimum value is {value}
dynamic.config.scale.out.threshold.required=Scale out load cannot be empty
dynamic.config.scale.out.threshold.range=Scale out load range is between 1-100%
dynamic.config.scale.in.minute.required=Scale in dimension cannot be empty
dynamic.config.scale.in.minute.min.value=Scale in dimension minimum value is {value}
dynamic.config.scale.in.threshold.required=Scale in load cannot be empty
dynamic.config.scale.in.threshold.range=Scale in load range is between 1-100%

# Dynamic config global validation
dynamic.config.global.zadig.endpoint.length.exceeded=Zadig endpoint length cannot exceed {max} characters
dynamic.config.global.zadig.api.token.length.exceeded=Zadig api token length cannot exceed {max} characters

# Host validation
host.ip.required=Host IP cannot be empty
host.ip.length.exceeded=Host IP length cannot exceed {max} characters
host.platform.length.exceeded=Platform length cannot exceed {max} characters
host.region.required=Region cannot be empty
host.region.length.exceeded=Region length cannot exceed {max} characters
host.remark.length.exceeded=Remark length cannot exceed {max} characters

# GPU validation
gpu.model.required=Model cannot be empty
gpu.card.count.required=Card count cannot be empty
gpu.card.count.min.value=Card count cannot be less than {value}
gpu.single.card.memory.required=Single card memory cannot be empty
gpu.single.card.memory.min.value=Single card memory cannot be less than {value}

# Model config validation
model.config.project.name.required=Project name cannot be empty
model.config.project.name.length.exceeded=Project name length cannot exceed {max} characters
model.config.project.git.url.required=Project git URL cannot be empty
model.config.project.git.url.length.exceeded=Project git URL length cannot exceed {max} characters
model.config.version.length.exceeded=Model version length cannot exceed {max} characters
model.config.sdk.version.required=Deploy SDK version cannot be empty
model.config.sdk.version.length.exceeded=Deploy SDK version length cannot exceed {max} characters
model.config.oss.url.required=Model file OSS URL cannot be empty
model.config.oss.url.length.exceeded=Model file OSS URL length cannot exceed {max} characters
model.config.encrypt.required=Encryption flag cannot be empty
model.config.mirror.image.url.required=Base mirror image URL cannot be empty
model.config.mirror.image.url.length.exceeded=Base mirror image URL length cannot exceed {max} characters
model.config.memory.size.required=Memory size GB cannot be empty
model.config.gpu.info.required=GPU information cannot be empty

# Model instance adjust validation
model.instance.adjust.model.info.required=Adjust model information cannot be empty

# Task model callback validation
task.model.callback.task.id.required=Task ID cannot be empty

# Model test validation
model.test.name.length.exceeded=Name length cannot exceed {max} characters
model.test.task.type.length.exceeded=Task type length cannot exceed {max} characters
model.test.model.name.length.exceeded=Model name length cannot exceed {max} characters
model.test.param.type.required=Parameter type cannot be empty
model.test.param.type.length.exceeded=Parameter type length cannot exceed {max} characters
model.test.model.params.required=Model parameters cannot be empty
model.test.batch.task.count.required=Batch task count cannot be empty
model.test.batch.task.count.min.value=Batch task count cannot be less than {value}
model.test.batch.task.count.max.value=Batch task count cannot exceed {value}

# Model test report validation
model.test.report.test.no.required=Test number cannot be empty
model.test.report.service.name.required=Service name cannot be empty
model.test.report.info.required=Report information cannot be empty

# Model encrypt callback validation
model.encrypt.callback.deploy.id.required=Deploy release ID cannot be empty

# Model heartbeat validation
model.heartbeat.register.id.required=Register ID cannot be empty


# Node validation
node.heartbeat.id.required=Register ID cannot be empty
node.register.node.ip.required=Node IP address cannot be empty
node.register.hostname.required=Hostname cannot be empty

validation.task.ids.empty=Task IDS cannot be empty
validation.params.null=Parameters cannot be null
validation.task.batch.range=Task batch flag must be 0 or 1

# Business error messages
business.sort.field.not.exists=Sort field [{0}] does not exist
business.model.param.type.error=Type error
business.model.param.format.error.object=Model parameter format error, should be {}
business.model.param.format.error.array=Model parameter format error, should be [{},{}]
business.file.upload.empty=Upload file cannot be empty
business.file.name.empty=File name cannot be empty
business.file.format.unsupported=Only support file formats {0}
business.file.upload.size.exceeded=Upload file size cannot exceed {0}
business.file.upload.failed=File upload failed
business.file.download.failed=File download failed
business.file.not.found=File not found
business.file.path.empty=File path cannot be empty
business.platform.unsupported=Unsupported cloud platform [{0}]
business.file.content.column.empty=r[{0}] file content [{1}] column cannot be empty
business.file.date.format.error=r[{0}]{1} format should be yyyy-MM, e.g.: 2025-06
business.bill.type.unsupported=Unsupported bill type [{0}]
business.bill.import.data.empty=Import bill type/list/cloud platform is empty
business.bill.no.matching.data=No matching bill data, please check bill data
business.task.info.empty=Task information is empty
business.task.create.failed=Task creation failed
business.task.model.name.ids.empty=modelName/taskIds is empty
business.task.id.model.name.empty=taskId/modelName is empty
business.task.id.empty=taskId cannot be empty
business.compare.type.empty=Compare type cannot be empty
business.task.type.model.name.empty=Task type/model name cannot be empty
business.model.param.format.error=Model parameter format error
business.model.param.empty=Model parameters cannot be empty
business.dict.type.exists=This dictionary type already exists and cannot be updated
business.model.test.status.cannot.operate=Non-[{0}] status cannot be operated, please refresh and try again
business.model.test.report.generated=Test report has been generated, please do not submit repeatedly
business.dynamic.adjust.status.cannot.cancel=Dynamic adjustment record status does not support cancellation
business.task.ids.business.id.empty=taskIds/businessId cannot be empty
business.batch.task.count.limit=Batch task count cannot exceed 200
business.bill.period.instance.duplicate=Period [{0}] instance id [{1}] duplicate, please check bill data
business.dict.type.already.exists=This dictionary type already exists
business.model.test.already.exists=This model test already exists, please execute first or wait for completion
business.compare.type.unsupported=Compare type not supported
business.resource.expend.month.data.exists=Current month expenditure data already exists
business.node.ip.duplicate=Node IP duplicate: {0}
business.deploy.status.only.deploying=Only deploying status deployment records are allowed
business.model.already.exists=This model already exists
business.model.not.supported.contact.admin=Unsupported model, please contact administrator
business.service.not.configured.contact.admin=Service not configured, please contact administrator
business.request.key.mismatch=Request key mismatch
business.cluster.disable.before.delete=Please disable the cluster first, then delete
business.approval.completed=Approval completed, unable to perform the operation again
business.upload.file.content.empty=The content of the uploaded file cannot be empty
business.service.type.not.match=The service type [{1}] marked by the [{0}] cluster is inconsistent with the registration information type [{3}] of the [{2}] model
business.current.month.plan.data.exists=The planning data for the current month already exists. Please delete it before adding it
business.task.not.exists=task not exists
business.param.together.empty=TaskId&taskType&modelName cannot be empty at the same time
business.notify.model.encrypt.failed=Notification model encryption failed
business.not.encrypting.status=Release records in non encrypted state
business.task.cancelled=The task has been cancelled and cannot be cancelled again
business.task.status.not.allow.cancel=Task status does not allow cancellation
business.model.test.not.allow.edit=The model test already exists and cannot be modified
business.test.task.running=The testing task is currently being executed. Please cancel it first and then delete it
business.not.allow.retry=No tasks that meet the criteria for retry
business.retry.result=Retry successful: {0}, retry failed: {1}
business.not.allow.cancel=No tasks that meet the criteria for cancellation
business.model.deploy.already.exists=This model deployment already exists, please deploy first or wait for deployment to complete
business.model.not.exists=This model does not exist
business.current.status.cannot.rollback=Current status cannot rolled back or data has changed, please retry
business.cluster.date.record.exists=This cluster already has a record on this date
business.cluster.date.record.exists.cannot.update=This cluster already has a record on this date, cannot update
business.gpu.in.use.cannot.delete=GPU is in use, cannot delete
business.host.ip.already.exists=Host IP already exists
business.host.enabled.cannot.delete=Enabled status, deletion not allowed, please disable first
business.deploy.draft.only.edit=Only draft status deployment records can be modified
business.deploy.deploying.cannot.delete=Deploying records cannot be deleted
business.deploy.draft.only.request=Only draft status deployment records can be requested for deployment
business.model.type.error=Type error
business.test.record.not.exists=Test record does not exist
business.model.config.not.exists=This model configuration does not exist
business.model.config.already.exists=Model configuration already exists
business.no.cluster.nodes=No cluster nodes, please check cluster configuration
