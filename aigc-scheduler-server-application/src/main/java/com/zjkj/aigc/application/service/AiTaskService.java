package com.zjkj.aigc.application.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.zjkj.aigc.application.config.TaskSourceProperties;
import com.zjkj.aigc.application.config.TritonProperties;
import com.zjkj.aigc.application.constant.StatusConstant;
import com.zjkj.aigc.application.dto.AtomModelTask;
import com.zjkj.aigc.application.mq.CustomChannelBinder;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.dto.TaskModelDTO;
import com.zjkj.aigc.common.dto.triton.TritonTaskReqDTO;
import com.zjkj.aigc.common.dto.triton.TritonTaskRespDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.task.TaskBatchEnum;
import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.model.TaskModelQuery;
import com.zjkj.aigc.common.req.model.TaskModelResultCallbackReq;
import com.zjkj.aigc.common.req.task.AigcTaskPriorityReq;
import com.zjkj.aigc.common.util.IdGenerator;
import com.zjkj.aigc.domain.config.propertie.BatchModelProperties;
import com.zjkj.aigc.domain.task.service.AigcTaskQueueService;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.infrastructure.mybatis.aigc.es.event.AigcTaskEvent;
import com.zjkj.aigc.task.dto.req.AiTaskStatusReq;
import com.zjkj.aigc.task.dto.req.BaseAiTaskReq;
import com.zjkj.aigc.task.dto.req.BatchGetAiTaskReq;
import com.zjkj.aigc.task.dto.req.BatchOperateReq;
import com.zjkj.aigc.task.dto.req.CreateAiTaskReq;
import com.zjkj.aigc.task.dto.req.GetAiTaskReq;
import com.zjkj.aigc.task.dto.resp.AiTaskStatusResp;
import com.zjkj.aigc.task.dto.resp.CreateAiTaskResp;
import com.zjkj.aigc.task.dto.resp.GetAiTaskResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MimeTypeUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class AiTaskService {

    @Value("${ai.task.priority.order:true}")
    private boolean orderByPriority;

    @Value("${ai.task.query.delayMillis:1000}")
    private long delayMillis;

    private final AigcTaskService aigcTaskService;
    private final AigcTaskDao aigcTaskDao;
    private final AtomModelService atomModelService;
    private final AiTaskNotifyService aiTaskNotifyService;
    private final BatchModelProperties batchModelProperties;
    private final TritonProperties tritonProperties;
    private final TaskSourceProperties taskSourceProperties;
    private final RestTemplate restTemplate;
    private final ApplicationEventPublisher eventPublisher;

    @Resource
    private CustomChannelBinder customChannelBinder;
    @Autowired
    private AigcTaskQueueService aigcTaskQueueService;

    /**
     * 参数校验
     *
     * @param req 请求参数
     * @param <T> 请求参数类型
     */
    public <T extends BaseAiTaskReq> void checkParam(T req) {
        if (!StringUtils.hasText(req.getTaskId())) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.task.id.empty"));
        }

        List<String> taskIds = Splitter.on(StringPool.COMMA).splitToList(req.getTaskId());
        checkParam(req.getBusinessId(), taskIds);
    }

    public void checkParam(String businessId, List<String> taskIds) {
        if (CollectionUtils.isEmpty(taskIds) || !StringUtils.hasText(businessId)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.task.ids.business.id.empty"));
        }

        if (taskIds.size() > 200) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.batch.task.count.limit"));
        }
    }

    /**
     * 检查模型参数
     *
     * @param params 参数
     */
    private void checkModelParams(Object params) {
        String paramJson;
        try {
            paramJson = JSON.toJSONString(params);
        } catch (Exception e) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.param.format.error"));
        }

        if (Objects.equals(paramJson, StringPool.EMPTY_JSON)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.param.empty"));
        }

        if (!JSON.isValidObject(paramJson)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.param.format.error"));
        }
    }

    /**
     * 创建任务
     *
     * @param req 请求参数
     * @param <T> 请求参数类型
     * @return 返回结果
     */
    public <T> CreateAiTaskResp createAiTask(CreateAiTaskReq<T> req) {
        checkModelParams(req.getParams());

        // 校验任务是否存在
        if (StringUtils.hasText(req.getTaskId())) {
            AigcTask task = aigcTaskDao.getByTaskId(req.getTaskId(), req.getBusinessId());
            if (Objects.nonNull(task)) {
                throw new BaseBizException(CustomErrorCode.DATA_ALREADY_EXISTS);
            }
        }

        // 模型批量优先级默认降级
        if (Objects.equals(req.getTaskBatch(), TaskBatchEnum.BATCH.code()) && Objects.isNull(req.getTaskPriority())) {
            req.setTaskPriority(batchModelProperties.getPriority());
        }

        // 保存任务
        AigcTask aigcTask = aigcTaskService.saveAigcTask(req);
        return new CreateAiTaskResp()
                .setTaskId(aigcTask.getTaskId())
                .setBusinessId(aigcTask.getBusinessId());
    }

    /**
     * 获取结果
     *
     * @param param 参数
     * @return 返回结果
     */
    public static Object getResult(String param) {
        return JSON.isValidObject(param) ? JSONObject.parseObject(param) : param;
    }


    /**
     * 转换任务
     * @param aigcTask 任务
     * @return GetAiTaskResp
     * @param <T> 返回参数类型
     */
    @SuppressWarnings("unchecked")
    public static <T, U> GetAiTaskResp<T, U> convertToAiTaskResp(AigcTask aigcTask) {
        GetAiTaskResp<T, U> resp = new GetAiTaskResp<>();
        resp.setProgress(aigcTask.getTaskProgress());
        resp.setRank(aigcTask.getRank());
        resp.setState(aigcTask.getTaskState());
        resp.setTaskType(aigcTask.getTaskType());
        resp.setModelName(aigcTask.getModelName());
        resp.setTaskId(aigcTask.getTaskId());
        resp.setBusinessId(aigcTask.getBusinessId());
        resp.setMessage(aigcTask.getFailMessage());
        resp.setInputParams((T) getResult(aigcTask.getModelParams()));
        resp.setOutput((U) getResult(aigcTask.getModelOutput()));
        resp.setCreatedTime(aigcTask.getCreatedTime());
        resp.setTaskStartTime(aigcTask.getTaskStartTime());
        resp.setTaskCompletionTime(aigcTask.getTaskCompletionTime());
        return resp;
    }

    /**
     * 获取任务
     * @param req 请求参数
     * @return 返回结果
     */
    public <T, U> GetAiTaskResp<T, U> newGetAiTask(GetAiTaskReq req) {
        boolean batchOpen = batchModelProperties.batchOpen(req.getTaskType(), req.getModelName());
        req.setBatchOpen(batchOpen);
        AigcTask aigcTask = aigcTaskService.getAiTask(req);
        if (Objects.isNull(aigcTask)) {
            return null;
        }
        return convertToAiTaskResp(aigcTask);
    }

    /**
     * 获取任务
     *
     * @param req 请求参数
     * @param <T> 返回参数类型
     * @return 返回结果
     */
    public <T, U> List<GetAiTaskResp<T, U>> batchGetAiTask(BatchGetAiTaskReq req) {
        checkParam(req.getBusinessId(), req.getTaskIds());
        List<AigcTask> aigcTaskList = aigcTaskService.getAigcTaskRankList(req);
        if (CollectionUtils.isEmpty(aigcTaskList)) {
            return Lists.newArrayList();
        }

        return aigcTaskList.stream()
                .map(AiTaskService::<T, U>convertToAiTaskResp)
                .collect(Collectors.toList());
    }


    /**
     * 获取任务状态
     *
     * @param req 请求参数
     * @return 返回结果
     */
    public List<AiTaskStatusResp> queryTaskStatusGroup(AiTaskStatusReq req) {
        AigcTaskCondition taskCondition = AigcTaskCondition.builder()
                .businessId(req.getBusinessId())
                .taskType(req.getTaskType())
                .modelName(req.getModelName())
                .build();
        List<AigcTask> aigcTaskStateGroupList = aigcTaskDao.queryTaskStateGroup(taskCondition);
        return aigcTaskStateGroupList.stream()
                .map(v -> new AiTaskStatusResp(v.getTaskState(), v.getCount()))
                .collect(Collectors.toList());
    }

    /**
     * 取消任务
     *
     * @param req 请求参数
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> cancelAiTask(BaseAiTaskReq req) {
        checkParam(req);
        AigcTaskCondition taskCondition = AigcTaskCondition.builder()
                .taskIds(Splitter.on(StringPool.COMMA).splitToList(req.getTaskId()))
                .businessId(req.getBusinessId())
                .build();
        List<AigcTask> aigcTaskList = aigcTaskDao.queryByCondition(taskCondition);
        if (CollectionUtils.isEmpty(aigcTaskList)) {
            throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS, MessageUtils.getMessage("business.task.not.exists"));
        }

        List<String> successList = new ArrayList<>();
        aigcTaskList.forEach(aigcTask -> {
            try {
                boolean cancelled = aigcTaskService.cancelAiTask(aigcTask);
                if (cancelled) {
                    successList.add(aigcTask.getTaskId());
                }
            } catch (BaseBizException ex) {
                log.info("取消任务失败, taskId:{}, error:{}", aigcTask.getTaskId(), ex.getMessage());
            }
        });

        return taskCondition.getTaskIds().stream()
                .filter(v -> !successList.contains(v))
                .collect(Collectors.toList());
    }


    /**
     * 路由模型批量任务
     * @param query 查询参数
     */
    public void routeModelBatch(TaskModelQuery query) {
        String modelName = query.getModelName();

        // 模型批量标识
        boolean isBatchModel = batchModelProperties.isBatchModel(modelName);
        if (isBatchModel) {
            modelName = batchModelProperties.removePrefix(modelName);
            query.setModelName(modelName);
            query.setTaskBatch(TaskBatchEnum.BATCH.code());
            return;
        }

        // 配置是否开启批量
        boolean batchOpen = batchModelProperties.batchOpen(query.getTaskType(), query.getModelName());
        if (batchOpen) {
            query.setTaskBatch(TaskBatchEnum.SINGLE.code());
        }

    }

    /**
     * 获取等待任务
     * @param query 查询参数
     * @return TaskModelDTO
     */
    public TaskModelDTO getWaitTaskOfBatch(TaskModelQuery query) {
        TaskModelDTO task = getWaitTask(query);
        if (Objects.nonNull(task)) {
            return task;
        }

        if (Objects.nonNull(query.getTaskBatch())) {
            // 无批量，倾斜处理对立任务
            Integer toggle = TaskBatchEnum.opposite(query.getTaskBatch());
            query.setTaskBatch(toggle);
            return getWaitTask(query);
        }

        return null;
    }

    /**
     * 回填任务队列
     *
     * @param id           任务ID
     * @param taskType     任务类型
     * @param modelName    模型名称
     * @param taskPriority 任务优先级
     */
    public void backFillTaskQueue(Long id, String taskType, String modelName, Integer taskPriority) {
        // 先从数据库查询完整的任务信息，获取businessId
        AigcTask existingTask = aigcTaskDao.getById(id);
        if (existingTask == null) {
            log.warn("回填任务队列失败，任务不存在: id={}", id);
            return;
        }

        // 检查businessId是否存在
        if (!StringUtils.hasText(existingTask.getBusinessId())) {
            log.warn("回填任务队列失败，任务缺少businessId: id={}, taskId={}", id, existingTask.getTaskId());
            return;
        }

        AigcTask aigcTask = new AigcTask();
        aigcTask.setId(id);
        aigcTask.setTaskType(taskType);
        aigcTask.setModelName(modelName);
        aigcTask.setBusinessId(existingTask.getBusinessId()); // 设置businessId
        aigcTask.setTaskPriority(Objects.nonNull(taskPriority) ? taskPriority : 0);

        try {
            aigcTaskQueueService.addIfAbsent(aigcTask);
            log.info("回填任务队列成功: id={}, businessId={}", id, existingTask.getBusinessId());
        } catch (Exception e) {
            log.error("回填任务队列异常: id={}, businessId={}, error={}", id, existingTask.getBusinessId(), e.getMessage());
        }
    }

    /**
     * 获取等待任务
     *
     * @param query 查询参数
     * @return TaskModelDTO
     */
    public TaskModelDTO getWaitTaskOfQueue(TaskModelQuery query) {
        Long id = aigcTaskQueueService.pop(query.getTaskType(), query.getModelName());
        if (Objects.isNull(id)) {
            return null;
        }

        AigcTask aigcTask = null;
        try {
            aigcTask = aigcTaskDao.getById(id);
            if (Objects.isNull(aigcTask)) {
                ThreadUtil.safeSleep(delayMillis);
                aigcTask = aigcTaskDao.getById(id);
                if (Objects.isNull(aigcTask)) {
                    backFillTaskQueue(id, query.getTaskType(), query.getModelName(), 0);
                    return null;
                }
            }

            if (!Objects.equals(aigcTask.getTaskState(), TaskStateEnum.WAITE.getCode())) {
                return null;
            }

            aigcTask.setContainerId(query.getContainerId());
            boolean started = aigcTaskService.startTask(aigcTask);
            if (started) {
                return convertToTaskModelDTO(aigcTask);
            }
        } catch (Exception e) {
            log.error("获取等待任务异常. taskType: {}, modelName:{}, error: {}",query.getTaskType(), query.getModelName(), e.getMessage());
            Integer taskPriority = Objects.nonNull(aigcTask) ? aigcTask.getTaskPriority() : null;
            backFillTaskQueue(id, query.getTaskType(), query.getModelName(), taskPriority);
            throw new RuntimeException("获取等待任务异常", e);
        }

        return null;
    }

    /**
     * 获取等待任务
     *
     * @param query 查询参数
     * @return TaskModelDTO
     */
    public TaskModelDTO getWaitTask(TaskModelQuery query) {
        AigcTaskCondition taskCondition = AigcTaskCondition.builder()
                .taskState(TaskStateEnum.WAITE.getCode())
                .taskType(query.getTaskType())
                .modelName(query.getModelName())
                .orderByPriority(orderByPriority)
                .taskBatch(query.getTaskBatch())
                .limit(10)
                .build();
        List<AigcTask> aigcTaskList = aigcTaskDao.queryByCondition(taskCondition);
        if (CollectionUtils.isEmpty(aigcTaskList)) {
            return null;
        }

        for (AigcTask aigcTask : aigcTaskList) {
            // 启动任务
            aigcTask.setContainerId(query.getContainerId());
            boolean started = aigcTaskService.startTask(aigcTask);
            if (started) {
                return convertToTaskModelDTO(aigcTask);
            }
        }

        return null;
    }

    /**
     * 转换模型任务
     * @param aigcTask 任务
     * @return TaskModelDTO
     */
    @SuppressWarnings("unchecked")
    private TaskModelDTO convertToTaskModelDTO(AigcTask aigcTask) {
        TaskModelDTO taskModelDTO = new TaskModelDTO();
        if (JSON.isValidObject(aigcTask.getModelParams())) {
            Map<String, Object> params = JSONObject.parseObject(aigcTask.getModelParams(), Map.class);
            taskModelDTO.putAll(params);
        }

        taskModelDTO.setBusinessTaskId(aigcTask.getTaskId());
        taskModelDTO.setTaskId(aigcTask.getAigcTaskId());
        boolean target = taskSourceProperties.isTarget(aigcTask.getTaskType(), aigcTask.getModelName());
        if (target) {
            TaskSourceProperties.Config config = taskSourceProperties.getAiConfig(aigcTask.getBusinessId(), aigcTask.getTaskSource());
            taskModelDTO.setAiConfig(config);
        }
        return taskModelDTO;
    }

    /**
     * 重置任务状态
     *
     * @param req 请求参数
     */
    public void resetTaskState(BaseAiTaskReq req) {
        AigcTask aigcTask = aigcTaskDao.getByTaskId(req.getTaskId(), req.getBusinessId());
        boolean restarted = restTask(aigcTask);
        if (!restarted) {
            throw new BaseBizException(CustomErrorCode.DATA_CHANGED);
        }
    }

    /**
     * 重置任务
     *
     * @param aigcTask 任务
     * @return 是否重置成功
     */
    private boolean restTask(AigcTask aigcTask) {
        if (Objects.isNull(aigcTask)) {
            throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS, MessageUtils.getMessage("business.task.not.exists"));
        }

        if (Objects.equals(aigcTask.getTaskState(), TaskStateEnum.SUCC.getCode())) {
            log.info("任务已完成, 不重置, taskId:{}", aigcTask.getTaskId());
            return true;
        }

        return aigcTaskService.restartTask(aigcTask);
    }

    /**
     * 检查任务是否成功
     *
     * @param code 任务状态
     * @return 是否成功
     */
    public boolean isModelSucc(Integer code) {
        return code != null && code == 0;
    }

    /**
     * 检查任务是否失败
     *
     * @param code 任务状态
     * @return 是否失败
     */
    public boolean isModelFail(Integer code) {
        return code != null && code < 0;
    }


    /**
     * 任务模型结果回调
     *
     * @param req 回调参数
     */
    public void taskResultCallback(TaskModelResultCallbackReq<Map<String, Object>> req) {
        AigcTask aigcTask = aigcTaskDao.getByAigcTaskId(req.getTaskId());
        if (Objects.isNull(aigcTask)) {
            log.info("callback. 任务不存在, aigcTaskId:{}", req.getTaskId());
            return;
        }

        //等待中的任务，先启动任务
        if (Objects.equals(TaskStateEnum.WAITE.getCode(), aigcTask.getTaskState())) {
            aigcTask.setContainerId(req.getContainerId());
            aigcTaskService.startTask(aigcTask);
            log.info("callback. 等待中的任务, 先启动, 已启动.. taskId:{}, aigcTaskId:{} ", aigcTask.getTaskId(), aigcTask.getAigcTaskId());
        }

        if (TaskStateEnum.isComplete(aigcTask.getTaskState())) {
            log.info("callback. 任务已最终状态, taskId: {}, aigcTaskId:{}", aigcTask.getTaskId(), aigcTask.getAigcTaskId());
            return;
        }

        Map<String, Object> infos = req.getInfos();
        if (CollectionUtils.isEmpty(infos) || Objects.isNull(infos.get("code"))) {
            log.info("callback. 任务结果/code为空, taskId:{}, aigcTaskId:{} ", aigcTask.getTaskId(), aigcTask.getAigcTaskId());
            return;
        }

        Integer code = (Integer) infos.get("code");
        Object respProgress = infos.get("progress");
        BigDecimal progressBig = BigDecimal.ZERO;
        if (Objects.nonNull(respProgress)) {
            progressBig = new BigDecimal(String.valueOf(respProgress));
        }

        aigcTask.setTaskProgress(progressBig.intValue());
        aigcTask.setContainerId(req.getContainerId());

        // 移除taskId
        infos.remove("taskId");
        infos.remove("businessTaskId");
        aigcTask.setModelOutput(JSON.toJSONString(infos));

        // 模型执行成功
        if (isModelSucc(code)) {
            aigcTaskService.succTask(aigcTask);
            notifyResult(aigcTask);
            return;
        }

        if (isModelFail(code)) {
            //判断模型执行结果是否是img output error,如果是则进行任务执行重试
            int retryCount = aigcTask.getRetryCount();
            if(code.equals(StatusConstant.IMG_OUTPUT_ERROR_CODE) || code.equals(StatusConstant.INFERENCE_ERROR_CODE)){
                log.info("taskId:{}, aigcTaskId:{}--任务执行报错 img output error或者Inference error，开始进行重试", aigcTask.getTaskId(), aigcTask.getAigcTaskId());
                boolean retry = aigcTaskService.retryTask(aigcTask);
                retryCount++;
                if (retry) {
                    log.info("taskId:{}, aigcTaskId:{}--任务重试:第{}次成功", aigcTask.getTaskId(), aigcTask.getAigcTaskId(), retryCount);
                    return;
                }
            }
            // 模型执行失败
            String message = "";
            if (infos.containsKey("message")) {
                message = (String) infos.get("message");
            } else if (infos.containsKey("msg")) {
                message = (String) infos.get("msg");
            }

            message = StrUtil.sub(message, 0,200);
            aigcTask.setFailMessage(message);
            aigcTaskService.failTask(aigcTask);
            notifyResult(aigcTask);
            return;
        }

        // 模型执行中
        aigcTaskService.progressTask(aigcTask);
    }

    /**
     * 通知结果
     *
     * @param aigcTask 任务
     */
    private void notifyResult(AigcTask aigcTask) {
        // 回调
        try {
            aiTaskNotifyService.notify(aigcTask);
        } catch (Exception e) {
            log.error("callback. 任务回调异常, taskId:{}, aigcTaskId:{} ", aigcTask.getTaskId(), aigcTask.getAigcTaskId(), e);
        }
    }

    /**
     * 批量重置任务状态
     *
     * @param req 请求参数
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchResetTaskState(BatchOperateReq req) {
        checkParam(req.getBusinessId(), req.getTaskIds());
        AigcTaskCondition taskCondition = AigcTaskCondition.builder()
                .taskIds(req.getTaskIds())
                .businessId(req.getBusinessId())
                .build();
        List<AigcTask> aigcTaskList = aigcTaskDao.queryByCondition(taskCondition);
        if (CollectionUtils.isEmpty(aigcTaskList)) {
            throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS, MessageUtils.getMessage("business.task.not.exists"));
        }

        List<String> successList = new ArrayList<>();
        aigcTaskList.forEach(aigcTask -> {
            try {
                boolean rested = restTask(aigcTask);
                if (rested) {
                    successList.add(aigcTask.getTaskId());
                }
            } catch (BaseBizException ex) {
                log.info("重置任务失败, taskId:{}, error:{}", aigcTask.getTaskId(), ex.getMessage());
            }
        });

        return taskCondition.getTaskIds().stream()
                .filter(v -> !successList.contains(v))
                .collect(Collectors.toList());
    }

    /**
     * 原子模型任务结果检查
     *
     * @param taskType      任务类型
     * @param modelNameList 模型名称列表
     */
    public void atomModelTaskResultCheck(String taskType, List<String> modelNameList) {
        if (!StringUtils.hasText(taskType) || CollectionUtils.isEmpty(modelNameList)) {
            log.info("atomModelTaskResultCheck() taskType/modelNameList is empty");
            return;
        }

        int maxSize = 200;
        int maxZeroCrt = 10;
        modelNameList.forEach(modelName -> {
            try {
                AigcTaskCondition condition = AigcTaskCondition.builder()
                        .taskType(taskType)
                        .modelName(modelName)
                        .taskState(TaskStateEnum.RUNNING.getCode())
                        .build();

                AtomicInteger zeroCrt = new AtomicInteger();
                List<AigcTask> aigcTaskList = new ArrayList<>(maxSize);
                aigcTaskDao.streamByCondition(condition, resultContext -> {
                    try {
                        AigcTask aigcTask = resultContext.getResultObject();
                        aigcTaskList.add(aigcTask);
                        if (aigcTaskList.size() >= maxSize) {
                            int updateSize = atomModelTaskResultCheckHandler(aigcTaskList, modelName);
                            aigcTaskList.clear();
                            if (updateSize == 0) {
                                zeroCrt.getAndIncrement();
                            }
                            if (zeroCrt.get() >= maxZeroCrt) {
                                resultContext.stop();
                                log.info("atomModelTaskResultCheck() taskType:{}, modelName:{}, 单次查询{}条, 达到{}次0更新. 终止", taskType, modelName, maxSize, maxZeroCrt);
                            }
                        }
                    } catch (Exception e) {
                        log.error("atomModelTaskResultCheck() 任务异常, taskType:{}", taskType, e);
                    }
                });

                // 处理剩余任务
                atomModelTaskResultCheckHandler(aigcTaskList, modelName);
            } catch (Exception e) {
                log.error("atomModelTaskResultCheck() 任务异常, taskType:{}, modelName:{}", taskType, modelName, e);
            }
        });
    }

    /**
     * 原子模型任务结果检查处理
     *
     * @param taskList       任务列表
     * @param collectionName 集合名称
     */
    public int atomModelTaskResultCheckHandler(List<AigcTask> taskList, String collectionName) {
        if (CollectionUtils.isEmpty(taskList) || !StringUtils.hasText(collectionName)) {
            return 0;
        }

        List<String> taskIds = taskList.stream()
                .map(AigcTask::getTaskId).
                collect(Collectors.toList());

        List<AtomModelTask<String>> atomModelTaskList = atomModelService.queryByIds(collectionName, taskIds);
        if (CollectionUtils.isEmpty(atomModelTaskList)) {
            return 0;
        }

        Map<String, AtomModelTask<String>> atomModelTaskMap = atomModelTaskList.stream()
                .collect(Collectors.toMap(AtomModelTask::getTaskId, v -> v, (k1, k2) -> k2));

        int taskProgress = 100;
        List<AigcTask> toUpdateList = taskList.stream()
                .filter(v -> atomModelTaskMap.containsKey(v.getTaskId()))
                .map(v -> {
                    AtomModelTask<String> atomModelTask = atomModelTaskMap.get(v.getTaskId());
                    AigcTask aigcTask = new AigcTask();
                    aigcTask.setId(v.getId());
                    aigcTask.setTaskProgress(taskProgress);
                    aigcTask.setTaskState(TaskStateEnum.SUCC.getCode());

                    JSONObject resultJson = JSONObject.parseObject(atomModelTask.getResult());
                    Integer code = resultJson.getInteger("code");
                    if (Objects.nonNull(code) && code < 0) {
                        aigcTask.setTaskState(TaskStateEnum.FAILED.getCode());
                        String message = StrUtil.sub(resultJson.getString("message"), 0,200);
                        aigcTask.setFailMessage(message);
                    }
                    LocalDateTime createTime = atomModelTask.getCreateTime();
                    LocalDateTime taskCompletionTime = Objects.nonNull(createTime) ? createTime : LocalDateTime.now();
                    aigcTask.setTaskCompletionTime(taskCompletionTime);
                    aigcTask.setModelOutput(JSONObject.parse(atomModelTask.getResult()).toJSONString());
                    return aigcTask;
                }).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(toUpdateList)) {
            aigcTaskDao.updateBatchById(toUpdateList);
        }

        log.info("atomModelTaskResultCheckHandler() taskType:{}, size:{} update_size:{}", collectionName, taskList.size(), toUpdateList.size());
        return toUpdateList.size();
    }

    /**
     * 原子模型任务启动
     *
     * @param taskType      任务类型
     * @param modelNameList 模型名称列表
     */
    public void atomModelTaskLaunch(String taskType, List<String> modelNameList) {
        if (!StringUtils.hasText(taskType) || CollectionUtils.isEmpty(modelNameList)) {
            log.info("atomModelTaskLaunch() taskType/modelNameList is empty");
            return;
        }
        AigcTaskCondition condition = AigcTaskCondition.builder()
                .taskType(taskType)
                .modelNameList(modelNameList)
                .taskState(TaskStateEnum.WAITE.getCode())
                .build();

        aigcTaskDao.streamByCondition(condition, resultContext -> {
            try {
                AigcTask aigcTask = resultContext.getResultObject();
                CreateAiTaskReq<JSONObject> taskReq = new CreateAiTaskReq<>();
                taskReq.setParams(JSONObject.parseObject(aigcTask.getModelParams()));
                taskReq.setTaskId(aigcTask.getTaskId());
                taskReq.setBusinessId(aigcTask.getBusinessId());
                taskReq.setTaskType(aigcTask.getTaskType());
                taskReq.setModelName(aigcTask.getModelName());
                atomModelService.pushTask(taskReq, () -> aigcTaskService.startTask(aigcTask));
            } catch (Exception e) {
                log.error("atomModelTaskLaunch() 任务异常, taskType:{}", taskType, e);
            }
        });

    }

    /**
     *  异步批量创建任务
     * @param reqList 请求参数
     * @param <T> 参数类型
     */
    public <T> void asyncBatchCreateAiTask(List<CreateAiTaskReq<T>> reqList) {
        Message<List<CreateAiTaskReq<T>>> message = MessageBuilder
                .withPayload(reqList)
                .setHeader(MessageHeaders.CONTENT_TYPE, MimeTypeUtils.APPLICATION_JSON)
                .build();
        customChannelBinder.sendAiTaskBatchCreate().send(message);
    }

    /**
     * 同步推理任务
     * @param req 请求参数
     * @param <U> 返回参数类型
     * @param <T> 模型入参类型
     * @return 返回结果
     */
    public <T, U, R> GetAiTaskResp<T, U> syncInferenceTask(CreateAiTaskReq<R> req) {
        // 校验模型参数
        checkModelParams(req.getParams());
        String idStr = IdGenerator.nexStrId();
        req.setTaskId(idStr);
        LocalDateTime now = LocalDateTime.now();

        // 任务推理
        TritonTaskRespDTO<Object> tritonTaskResp = executeTritonTask(req);
        AigcTask aigcTask = new AigcTask()
                .setBusinessId(req.getBusinessId())
                .setTaskType(req.getTaskType())
                .setModelName(req.getModelName())
                .setTaskId(idStr)
                .setAigcTaskId(idStr)
                .setCreatedTime(now)
                .setTaskStartTime(now)
                .setTaskCompletionTime(LocalDateTime.now())
                .setModelParams(JSON.toJSONString(req.getParams()))
                .setTaskSource(StringPool.EMPTY);

        if (Objects.equals(tritonTaskResp.getCode(), 0)) {
            aigcTask.setTaskState(TaskStateEnum.SUCC.getCode());
            aigcTask.setTaskProgress(100);
            aigcTask.setModelOutput(JSON.toJSONString(tritonTaskResp.getResult()));
        } else {
            aigcTask.setTaskState(TaskStateEnum.FAILED.getCode());
            aigcTask.setTaskProgress(0);
            aigcTask.setFailMessage(StrUtil.sub(tritonTaskResp.getMessage(), 0, 200));
            log.info("syncInferenceTask(). inference failure taskId:{}, resp:{}", idStr, JSON.toJSONString(tritonTaskResp));
        }

        aigcTaskDao.save(aigcTask);
        eventPublisher.publishEvent(new AigcTaskEvent(aigcTask));
        return convertToAiTaskResp(aigcTask);
    }

    /**
     * 执行Triton任务
     *
     * @param req 请求参数
     * @return 返回结果
     */
    @SuppressWarnings("unchecked")
    public <T> TritonTaskRespDTO<Object> executeTritonTask(CreateAiTaskReq<T> req) {
        String appName = tritonProperties.getAppName(req.getTaskType(), req.getModelName());
        BaseBizException.isTrue(StringUtils.hasText(appName), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.not.supported.contact.admin"));

        String resultEndpoint = tritonProperties.getResultEndpoint(appName);
        BaseBizException.isTrue(StringUtils.hasText(resultEndpoint), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.service.not.configured.contact.admin"));

        TritonTaskReqDTO<Object> taskReqDTO = new TritonTaskReqDTO<>()
                .setAppName(appName)
                .setType(tritonProperties.getType(req.getTaskType(), req.getModelName()))
                .setParams(JSON.toJSON(req.getParams()))
                .setTaskId(req.getTaskId());

        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<TritonTaskReqDTO<Object>> request = new HttpEntity<>(taskReqDTO, httpHeaders);
            return restTemplate.postForObject(resultEndpoint, request, TritonTaskRespDTO.class);
        } catch (Exception ex) {
            log.error("executeTritonTask 请求异常, taskId:{}, endpoint:{}", taskReqDTO.getTaskId(), resultEndpoint, ex);
            return TritonTaskRespDTO.defaultFail(ex.getMessage());
        }
    }

    /**
     * 设置任务优先级
     * @param req 请求参数
     * @return 优先级
     */
    @Transactional(rollbackFor = Exception.class)
    public int priority(AigcTaskPriorityReq req) {
        if (CollectionUtils.isEmpty(req.getTaskIds()) && (!StringUtils.hasText(req.getTaskType()) || !StringUtils.hasText(req.getModelName()))) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.param.together.empty"));
        }

        AigcTaskCondition condition = AigcTaskCondition.builder()
                .selectColumns(List.of(AigcTask::getId, AigcTask::getTaskType, AigcTask::getModelName, AigcTask::getTaskPriority))
                .businessId(req.getBusinessId())
                .taskState(TaskStateEnum.WAITE.getCode())
                .build();
        if (!CollectionUtils.isEmpty(req.getTaskIds())) {
            condition.setTaskIds(req.getTaskIds());
        } else {
            condition.setTaskType(req.getTaskType());
            condition.setModelName(req.getModelName());
        }

        List<AigcTask> aigcTasks = aigcTaskService.queryList(condition).stream()
                .filter(v -> !Objects.equals(v.getTaskPriority(), req.getTargetPriority()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aigcTasks)) {
            return 0;
        }

        Set<Long> ids = CollStreamUtil.toSet(aigcTasks, AigcTask::getId);
        aigcTaskService.updateTaskPriority(ids, req.getTargetPriority());

        aigcTasks.forEach(task -> task.setTaskPriority(req.getTargetPriority()));
        aigcTaskQueueService.batchUpdate(aigcTasks);
        return ids.size();
    }
}
