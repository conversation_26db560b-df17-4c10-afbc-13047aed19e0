# AIGC任务调度系统技术文档

## 📋 目录
1. [系统概述](#1-系统概述)
2. [任务创建流程分析](#2-任务创建流程分析)
3. [公平调度队列机制](#3-公平调度队列机制)
4. [模型SDK任务获取流程](#4-模型sdk任务获取流程)
5. [完整数据流转图](#5-完整数据流转图)
6. [关键代码片段](#6-关键代码片段)
7. [实际运行示例](#7-实际运行示例)
8. [监控和运维](#8-监控和运维)

---

## 1. 🎯 系统概述

AIGC任务调度系统是一个基于Redis队列的分布式任务调度平台，支持多业务线的公平调度。系统通过businessId对任务进行分组，采用轮询算法确保各业务线获得公平的执行机会。

### 核心特性
- **🔄 公平调度**：基于businessId的轮询调度算法
- **📊 高性能**：Redis SortedSet实现优先级队列
- **🎛️ 多业务线**：支持动态业务线管理
- **⚖️ 负载均衡**：防止单一业务线占用全部资源
- **📈 可观测**：完整的监控和统计接口

### 系统架构
```
外部调用方 → AiTaskService → AigcTaskQueueService → Redis队列
                    ↓                    ↓
                数据库存储          公平调度算法
                    ↑                    ↑
模型SDK ← ModelController ← getWaitTaskOfQueue ← pop()方法
```

---

## 2. 📝 任务创建流程分析

### 2.1 单个任务创建流程

**调用链路**：
```
外部调用 → AiTaskService.createAiTask() → AigcTaskService.saveAigcTask() → AigcTaskQueueService.add()
```

**详细步骤**：

#### 步骤1：参数验证和预处理
```java
public <T> CreateAiTaskResp createAiTask(CreateAiTaskReq<T> req) {
    // 1. 验证模型参数
    checkModelParams(req.getParams());
    
    // 2. 检查任务是否已存在
    if (StringUtils.hasText(req.getTaskId())) {
        AigcTask task = aigcTaskDao.getByTaskId(req.getTaskId(), req.getBusinessId());
        if (Objects.nonNull(task)) {
            throw new BaseBizException(CustomErrorCode.DATA_ALREADY_EXISTS);
        }
    }
    
    // 3. 批量任务优先级降级处理
    if (Objects.equals(req.getTaskBatch(), TaskBatchEnum.BATCH.code()) && 
        Objects.isNull(req.getTaskPriority())) {
        req.setTaskPriority(batchModelProperties.getPriority());
    }
}
```

#### 步骤2：任务数据保存
```java
// AigcTaskService.saveAigcTask()
public AigcTask saveAigcTask(CreateAiTaskReq<?> req) {
    AigcTask aigcTask = new AigcTask();
    // 设置基本信息
    aigcTask.setBusinessId(req.getBusinessId());  // 🔑 关键：businessId
    aigcTask.setTaskType(req.getTaskType());
    aigcTask.setModelName(req.getModelName());
    aigcTask.setTaskPriority(req.getTaskPriority());
    aigcTask.setTaskState(TaskStateEnum.WAITE.getCode());
    
    // 保存到数据库
    save(aigcTask);
    
    // 🚀 添加到Redis队列
    aigcTaskQueueService.add(aigcTask);
    
    return aigcTask;
}
```

#### 步骤3：添加到公平调度队列
```java
// AigcTaskQueueService.add()
public void add(AigcTask aigcTask) {
    String businessId = aigcTask.getBusinessId();
    if (!StringUtils.hasText(businessId)) {
        throw new IllegalArgumentException("businessId不能为空");
    }
    
    // 计算任务分数（优先级 + 时间戳）
    double taskScore = getTaskScore(aigcTask.getId(), aigcTask.getTaskPriority());
    
    // 生成业务线队列Key
    String queueKey = getBusinessQueueKey(aigcTask.getTaskType(), 
                                         aigcTask.getModelName(), 
                                         businessId);
    
    // 添加到Redis SortedSet
    RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(queueKey);
    boolean added = sortedSet.add(taskScore, aigcTask.getId());
    
    // 注册业务线到活跃列表
    registerBusinessId(aigcTask.getTaskType(), aigcTask.getModelName(), businessId);
}
```

### 2.2 批量任务创建流程

**调用链路**：
```
外部调用 → AiTaskService.batchCreateAiTask() → AigcTaskService.saveBatch() → AigcTaskQueueService.batchAdd()
```

**关键实现**：
```java
public <T> void batchCreateAiTask(List<CreateAiTaskReq<T>> reqList) {
    // 1. 批量构建任务对象
    List<AigcTask> aigcTasks = reqList.stream()
            .map(this::buildAigcTask)
            .collect(Collectors.toList());
    
    // 2. 批量保存到数据库
    aigcTaskService.saveBatch(aigcTasks);
    
    // 3. 批量添加到队列（按businessId分组）
    aigcTaskQueueService.batchAdd(aigcTasks);
}
```

**批量队列添加逻辑**：
```java
public void batchAdd(Collection<AigcTask> aigcTaskList) {
    // 按 taskType + modelName + businessId 分组
    Map<String, List<AigcTask>> groupedTasks = aigcTaskList.stream()
            .collect(Collectors.groupingBy(task -> {
                String businessId = task.getBusinessId();
                if (!StringUtils.hasText(businessId)) {
                    throw new IllegalArgumentException("businessId不能为空");
                }
                return getBusinessQueueKey(task.getTaskType(), task.getModelName(), businessId);
            }));

    // 分组批量添加到Redis
    groupedTasks.forEach((queueKey, tasks) -> {
        Map<Double, Long> taskMap = tasks.stream()
                .collect(Collectors.toMap(
                        task -> getTaskScore(task.getId(), task.getTaskPriority()),
                        AigcTask::getId
                ));

        RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(queueKey);
        int addedCount = sortedSet.addAll(taskMap);
        
        // 注册业务线
        if (!tasks.isEmpty()) {
            AigcTask firstTask = tasks.get(0);
            registerBusinessId(firstTask.getTaskType(), firstTask.getModelName(), 
                             firstTask.getBusinessId());
        }
    });
}
```

---

## 3. ⚖️ 公平调度队列机制

### 3.1 Redis Key结构设计

**新的队列Key格式**：
```
aigc:task:queue:{taskType}:{modelName}:{businessId}
```

**辅助Key结构**：
```
# 活跃业务线集合
aigc:task:active_business:{taskType}:{modelName}

# 轮询索引
aigc:task:round_robin_index:{taskType}:{modelName}
```

**实际示例**：
```
# 队列Key
aigc:task:queue:text-generation:gpt-3.5:ecommerce-system
aigc:task:queue:text-generation:gpt-3.5:content-audit
aigc:task:queue:text-generation:gpt-3.5:user-service

# 活跃业务线集合
aigc:task:active_business:text-generation:gpt-3.5
-> {"ecommerce-system", "content-audit", "user-service"}

# 轮询索引
aigc:task:round_robin_index:text-generation:gpt-3.5 -> 1
```

### 3.2 业务线管理机制

**注册业务线**：
```java
private void registerBusinessId(String taskType, String modelName, String businessId) {
    String activeSetKey = getActiveBusinessSetKey(taskType, modelName);
    RSet<String> activeSet = redissonClient.getSet(activeSetKey);
    boolean added = activeSet.add(businessId);
    
    if (added) {
        // 设置24小时过期时间，防止内存泄漏
        activeSet.expire(24, TimeUnit.HOURS);
    }
}
```

**移除空队列业务线**：
```java
private void removeBusinessIdFromActive(String taskType, String modelName, String businessId) {
    String activeSetKey = getActiveBusinessSetKey(taskType, modelName);
    RSet<String> activeSet = redissonClient.getSet(activeSetKey);
    activeSet.remove(businessId);
}
```

### 3.3 轮询调度算法

**核心算法实现**：
```java
public Long pop(String taskType, String modelName) {
    // 1. 获取所有活跃业务线
    Set<String> activeBusinessIds = getActiveBusinessIds(taskType, modelName);
    if (activeBusinessIds.isEmpty()) {
        return null;
    }

    // 2. 获取当前轮询索引
    String modelKey = getModelKey(taskType, modelName);
    long currentIndex = getRoundRobinIndex(modelKey);
    
    // 3. 转换为有序列表（确保一致性）
    List<String> businessIdList = new ArrayList<>(activeBusinessIds);
    Collections.sort(businessIdList);
    
    // 4. 轮询获取任务
    int startIndex = (int) (currentIndex % businessIdList.size());
    for (int i = 0; i < businessIdList.size(); i++) {
        int currentIdx = (startIndex + i) % businessIdList.size();
        String businessId = businessIdList.get(currentIdx);
        
        Long taskId = popFromBusinessQueue(taskType, modelName, businessId);
        if (taskId != null) {
            // 更新轮询索引到下一个业务线
            updateRoundRobinIndex(modelKey, currentIdx + 1);
            return taskId;
        }
    }
    
    return null;
}
```

**轮询索引管理**：
```java
private long getRoundRobinIndex(String modelKey) {
    String indexKey = getRoundRobinIndexKey(modelKey);
    RAtomicLong atomicLong = redissonClient.getAtomicLong(indexKey);
    return atomicLong.get();
}

private void updateRoundRobinIndex(String modelKey, long newIndex) {
    String indexKey = getRoundRobinIndexKey(modelKey);
    RAtomicLong atomicLong = redissonClient.getAtomicLong(indexKey);
    atomicLong.set(newIndex);
    atomicLong.expire(1, TimeUnit.HOURS);
}
```

---

## 4. 🔄 模型SDK任务获取流程

### 4.1 调用链路

```
模型SDK → ModelController.queryAigcTask() → AiTaskService.getWaitTaskOfQueue() → AigcTaskQueueService.pop()
```

### 4.2 详细执行步骤

#### 步骤1：ModelController入口
```java
@PostMapping("/queryAigcTask")
public TaskModelDTO queryAigcTask(@RequestBody TaskModelQuery query) {
    // 路由模型批量任务
    aiTaskService.routeModelBatch(query);
    
    // 获取等待任务
    return aiTaskService.getWaitTaskOfBatch(query);
}
```

#### 步骤2：任务获取逻辑
```java
public TaskModelDTO getWaitTaskOfQueue(TaskModelQuery query) {
    // 🔑 关键：调用公平调度的pop方法
    Long id = aigcTaskQueueService.pop(query.getTaskType(), query.getModelName());
    if (Objects.isNull(id)) {
        return null;
    }

    AigcTask aigcTask = null;
    try {
        // 从数据库获取完整任务信息
        aigcTask = aigcTaskDao.getById(id);
        if (Objects.isNull(aigcTask)) {
            // 任务不存在，回填队列
            backFillTaskQueue(id, query.getTaskType(), query.getModelName(), 0);
            return null;
        }

        // 验证任务状态
        if (!Objects.equals(aigcTask.getTaskState(), TaskStateEnum.WAITE.getCode())) {
            return null;
        }

        // 🚀 启动任务（状态：WAITE → RUNNING）
        aigcTask.setContainerId(query.getContainerId());
        boolean started = aigcTaskService.startTask(aigcTask);
        if (started) {
            return convertToTaskModelDTO(aigcTask);
        }
    } catch (Exception e) {
        // 异常处理：回填队列
        Integer taskPriority = Objects.nonNull(aigcTask) ? aigcTask.getTaskPriority() : null;
        backFillTaskQueue(id, query.getTaskType(), query.getModelName(), taskPriority);
        throw new RuntimeException("获取等待任务异常", e);
    }

    return null;
}
```

#### 步骤3：任务状态转换
```java
// AigcTaskService.startTask()
public boolean startTask(AigcTask aigcTask) {
    // 乐观锁更新任务状态
    AigcTask updateTask = new AigcTask();
    updateTask.setId(aigcTask.getId());
    updateTask.setTaskState(TaskStateEnum.RUNNING.getCode());
    updateTask.setTaskStartTime(LocalDateTime.now());
    updateTask.setContainerId(aigcTask.getContainerId());
    
    // 只有WAITE状态的任务才能启动
    boolean updated = updateByIdAndState(updateTask, TaskStateEnum.WAITE.getCode());
    
    if (updated) {
        // 发布任务启动事件
        applicationEventPublisher.publishEvent(new AigcTaskEvent(aigcTask, AigcTaskEvent.START));
    }
    
    return updated;
}
```

---

## 5. 📊 完整数据流转图

### 5.1 任务创建到获取的完整流程

```mermaid
graph TD
    A[外部调用方] --> B[AiTaskService.createAiTask]
    B --> C[参数验证]
    C --> D[AigcTaskService.saveAigcTask]
    D --> E[保存到数据库]
    D --> F[AigcTaskQueueService.add]
    F --> G[按businessId分组]
    G --> H[添加到Redis队列]
    H --> I[注册业务线]
    
    J[模型SDK] --> K[ModelController.queryAigcTask]
    K --> L[AiTaskService.getWaitTaskOfQueue]
    L --> M[AigcTaskQueueService.pop]
    M --> N[获取活跃业务线]
    N --> O[轮询调度算法]
    O --> P[从业务线队列获取任务]
    P --> Q[更新轮询索引]
    Q --> R[返回任务ID]
    R --> S[从数据库获取完整信息]
    S --> T[启动任务 WAITE→RUNNING]
    T --> U[返回TaskModelDTO]
    
    style G fill:#e1f5fe
    style O fill:#f3e5f5
    style T fill:#e8f5e8
```

### 5.2 公平调度核心逻辑

```mermaid
graph LR
    A[获取任务请求] --> B[获取活跃业务线列表]
    B --> C[获取当前轮询索引]
    C --> D[计算起始业务线]
    D --> E[轮询各业务线队列]
    E --> F{队列是否有任务?}
    F -->|有| G[获取任务]
    F -->|无| H[尝试下一个业务线]
    H --> I{是否遍历完所有业务线?}
    I -->|否| E
    I -->|是| J[返回null]
    G --> K[更新轮询索引]
    K --> L[移除空队列业务线]
    L --> M[返回任务ID]
    
    style E fill:#fff3e0
    style G fill:#e8f5e8
    style K fill:#f3e5f5
```

---

## 6. 💻 关键代码片段

### 6.1 businessId在流程中的作用

**任务创建时**：
```java
// 必须设置businessId
CreateAiTaskReq<Object> req = new CreateAiTaskReq<>();
req.setBusinessId("ecommerce-system");  // 🔑 业务线标识
req.setTaskType("text-generation");
req.setModelName("gpt-3.5");
```

**队列Key生成**：
```java
private String getBusinessQueueKey(String taskType, String modelName, String businessId) {
    return String.format("aigc:task:queue:%s:%s:%s", taskType, modelName, businessId);
    // 结果：aigc:task:queue:text-generation:gpt-3.5:ecommerce-system
}
```

**公平调度实现**：
```java
// 轮询所有业务线
List<String> businessIdList = new ArrayList<>(activeBusinessIds);
Collections.sort(businessIdList); // 确保顺序一致性

int startIndex = (int) (currentIndex % businessIdList.size());
for (int i = 0; i < businessIdList.size(); i++) {
    int currentIdx = (startIndex + i) % businessIdList.size();
    String businessId = businessIdList.get(currentIdx);  // 🔄 轮询业务线
    
    Long taskId = popFromBusinessQueue(taskType, modelName, businessId);
    if (taskId != null) {
        updateRoundRobinIndex(modelKey, currentIdx + 1);  // 📈 更新索引
        return taskId;
    }
}
```

### 6.2 轮询算法确保公平性

**索引计算逻辑**：
```java
// 假设有3个业务线：[A, B, C]，当前索引为1
int startIndex = 1 % 3 = 1;  // 从业务线B开始

// 轮询顺序：B(1) → C(2) → A(0)
for (int i = 0; i < 3; i++) {
    int currentIdx = (1 + i) % 3;
    // i=0: currentIdx = 1 (业务线B)
    // i=1: currentIdx = 2 (业务线C)  
    // i=2: currentIdx = 0 (业务线A)
}
```

**动态业务线处理**：
```java
// 业务线队列为空时自动移除
if (sortedSet.size() == 0) {
    removeBusinessIdFromActive(taskType, modelName, businessId);
}

// 新业务线自动加入
private void registerBusinessId(String taskType, String modelName, String businessId) {
    String activeSetKey = getActiveBusinessSetKey(taskType, modelName);
    RSet<String> activeSet = redissonClient.getSet(activeSetKey);
    activeSet.add(businessId);  // 自动加入活跃列表
}
```

---

## 7. 🎯 实际运行示例

### 7.1 业务场景设置

**初始状态**：
- 业务线A（电商系统）：10000个任务
- 业务线B（内容审核）：1个任务
- 业务线C（用户服务）：1个任务

**Redis数据结构**：
```redis
# 队列数据
ZCARD aigc:task:queue:text-generation:gpt-3.5:ecommerce-system    # 10000
ZCARD aigc:task:queue:text-generation:gpt-3.5:content-audit       # 1
ZCARD aigc:task:queue:text-generation:gpt-3.5:user-service        # 1

# 活跃业务线
SMEMBERS aigc:task:active_business:text-generation:gpt-3.5
# ["content-audit", "ecommerce-system", "user-service"]

# 轮询索引
GET aigc:task:round_robin_index:text-generation:gpt-3.5
# "0"
```

### 7.2 调度执行序列

**第1轮调度**：
```
索引=0, 业务线列表=["content-audit", "ecommerce-system", "user-service"]
起始位置=0 → content-audit队列 → 获取任务ID=1001
更新索引=1
```

**第2轮调度**：
```
索引=1, 业务线列表=["content-audit", "ecommerce-system", "user-service"]  
起始位置=1 → ecommerce-system队列 → 获取任务ID=2001
更新索引=2
```

**第3轮调度**：
```
索引=2, 业务线列表=["content-audit", "ecommerce-system", "user-service"]
起始位置=2 → user-service队列 → 获取任务ID=3001  
更新索引=0
```

**第4轮调度**：
```
索引=0, 业务线列表=["ecommerce-system"]  # content-audit和user-service队列已空
起始位置=0 → ecommerce-system队列 → 获取任务ID=2002
更新索引=1
```

### 7.3 监控接口返回示例

**队列统计接口**：
```bash
curl "http://localhost:8080/aigc/task-queue/stats?taskType=text-generation&modelName=gpt-3.5"
```

**返回结果**：
```json
{
  "taskType": "text-generation",
  "modelName": "gpt-3.5",
  "activeBusinessCount": 1,
  "activeBusinessIds": ["ecommerce-system"],
  "businessQueueSizes": {
    "ecommerce-system": 9998
  },
  "totalTasks": 9998,
  "currentRoundRobinIndex": 1
}
```

---

## 8. 📊 监控和运维

### 8.1 监控接口

**获取队列统计**：
```bash
GET /aigc/task-queue/stats?taskType={taskType}&modelName={modelName}
```

**刷新业务线列表**：
```bash
POST /aigc/task-queue/refresh?taskType={taskType}&modelName={modelName}
```

**清理缓存**：
```bash
POST /aigc/task-queue/clear-cache?taskType={taskType}&modelName={modelName}
```

### 8.2 关键指标监控

**业务公平性指标**：
- 各业务线任务执行比例
- 轮询索引变化频率
- 业务线队列长度分布

**系统性能指标**：
- 任务获取平均延迟
- Redis队列操作QPS
- 任务状态转换成功率

**异常监控**：
- businessId为空的任务数量
- 队列回填操作频率
- 任务启动失败率

---

## 📝 总结

AIGC任务调度系统通过基于businessId的公平调度机制，成功解决了多业务线间的资源竞争问题。系统具备以下核心优势：

✅ **公平性保证**：轮询算法确保每个业务线都有平等的执行机会
✅ **高性能**：Redis SortedSet提供高效的优先级队列
✅ **动态扩展**：支持业务线的动态增减
✅ **故障恢复**：完善的异常处理和队列回填机制
✅ **可观测性**：丰富的监控接口和统计信息

该架构设计既保证了系统的公平性，又维持了高性能和可扩展性，为AIGC服务的稳定运行提供了坚实的基础。

---

*文档版本：v1.0*  
*更新时间：2025-01-11*  
*维护团队：AIGC调度系统开发组*
