package com.zjkj.aigc;

import com.zjkj.aigc.domain.task.service.AigcTaskQueueService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 公平调度测试
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@SpringBootTest
@SpringJUnitConfig
public class FairSchedulingTest {

    @Resource
    private AigcTaskQueueService aigcTaskQueueService;

    @Test
    public void testFairScheduling() {
        String taskType = "text-generation";
        String modelName = "gpt-3.5";
        
        // 清理之前的测试数据
        aigcTaskQueueService.clearModelCache(taskType, modelName);
        
        // 创建测试任务
        List<AigcTask> tasks = new ArrayList<>();
        
        // 业务线A：10个任务
        for (int i = 1; i <= 10; i++) {
            AigcTask task = new AigcTask();
            task.setId((long) i);
            task.setTaskId("task_a_" + i);
            task.setBusinessId("business_a");
            task.setTaskType(taskType);
            task.setModelName(modelName);
            task.setTaskPriority(0);
            tasks.add(task);
        }
        
        // 业务线B：1个任务
        AigcTask taskB = new AigcTask();
        taskB.setId(11L);
        taskB.setTaskId("task_b_1");
        taskB.setBusinessId("business_b");
        taskB.setTaskType(taskType);
        taskB.setModelName(modelName);
        taskB.setTaskPriority(0);
        tasks.add(taskB);
        
        // 业务线C：1个任务
        AigcTask taskC = new AigcTask();
        taskC.setId(12L);
        taskC.setTaskId("task_c_1");
        taskC.setBusinessId("business_c");
        taskC.setTaskType(taskType);
        taskC.setModelName(modelName);
        taskC.setTaskPriority(0);
        tasks.add(taskC);
        
        // 批量添加任务
        log.info("开始批量添加任务...");
        aigcTaskQueueService.batchAdd(tasks);
        
        // 获取初始统计信息
        Map<String, Object> initialStats = aigcTaskQueueService.getQueueStats(taskType, modelName);
        log.info("初始队列统计: {}", initialStats);
        
        // 验证公平调度
        log.info("开始验证公平调度...");
        List<Long> executionOrder = new ArrayList<>();
        Long taskId;
        int count = 0;
        while ((taskId = aigcTaskQueueService.pop(taskType, modelName)) != null && count < 12) {
            executionOrder.add(taskId);
            count++;
            
            // 打印执行顺序
            log.info("第{}次获取任务ID: {}", count, taskId);
            
            // 获取当前统计信息
            Map<String, Object> stats = aigcTaskQueueService.getQueueStats(taskType, modelName);
            log.info("当前队列统计: {}", stats);
            
            // 避免无限循环
            if (count >= 20) {
                break;
            }
        }
        
        // 验证执行顺序是否公平
        log.info("完整执行顺序: {}", executionOrder);
        
        // 验证前3个任务应该分别来自不同的业务线
        if (executionOrder.size() >= 3) {
            log.info("前3个任务的执行顺序验证:");
            log.info("第1个任务ID: {} (期望来自不同业务线)", executionOrder.get(0));
            log.info("第2个任务ID: {} (期望来自不同业务线)", executionOrder.get(1));
            log.info("第3个任务ID: {} (期望来自不同业务线)", executionOrder.get(2));
        }
        
        // 最终统计
        Map<String, Object> finalStats = aigcTaskQueueService.getQueueStats(taskType, modelName);
        log.info("最终队列统计: {}", finalStats);
        
        log.info("公平调度测试完成！");
    }
    
    @Test
    public void testQueueStats() {
        String taskType = "text-generation";
        String modelName = "gpt-3.5";
        
        // 获取队列统计信息
        Map<String, Object> stats = aigcTaskQueueService.getQueueStats(taskType, modelName);
        log.info("队列统计信息: {}", stats);
        
        // 刷新活跃业务线列表
        aigcTaskQueueService.refreshActiveBusinessIds(taskType, modelName);
        
        // 再次获取统计信息
        Map<String, Object> refreshedStats = aigcTaskQueueService.getQueueStats(taskType, modelName);
        log.info("刷新后的队列统计信息: {}", refreshedStats);
    }
}
