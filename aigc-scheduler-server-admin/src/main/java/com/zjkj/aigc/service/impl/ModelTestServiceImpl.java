package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.model.ModelTestParamTypeEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.model.test.ModelTestCreateReq;
import com.zjkj.aigc.common.req.model.test.ModelTestQuery;
import com.zjkj.aigc.common.vo.ModelTestParamVO;
import com.zjkj.aigc.common.vo.ModelTestReportInfo;
import com.zjkj.aigc.common.vo.ModelTestReportVO;
import com.zjkj.aigc.common.vo.ModelTestVO;
import com.zjkj.aigc.domain.task.service.ModelAutoTestService;
import com.zjkj.aigc.domain.utils.SpringUtils;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.ModelAutoTestRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestParam;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestReport;
import com.zjkj.aigc.service.ModelTestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ModelTestServiceImpl implements ModelTestService {

    private final ModelAutoTestService modelAutoTestService;

    @Override
    public Page<ModelTestVO> page(Page<ModelTestVO> rePage, ModelTestQuery query) {
        Page<ModelAutoTestRecord> reqPage = Page.of(rePage.getCurrent(), rePage.getSize());
        reqPage.setOrders(rePage.orders());

        ModelAutoTestRecordCondition condition = ModelAutoTestRecordCondition.builder()
                .name(query.getName())
                .taskType(query.getTaskType())
                .modelName(query.getModelName())
                .build();

        if (StringUtils.hasText(query.getTestNo())) {
            condition.setTestNoList(Lists.newArrayList(query.getTestNo()));
        }

        if (Objects.nonNull(query.getStatus())) {
            condition.setStatusList(Lists.newArrayList(query.getStatus()));
        }

        Page<ModelAutoTestRecord> resultPage = modelAutoTestService.queryRecordByPage(reqPage, condition);
        rePage.setPages(resultPage.getPages());
        rePage.setTotal(resultPage.getTotal());

        List<ModelTestVO> modelTestVOList = BeanUtil.copyToList(resultPage.getRecords(), ModelTestVO.class);
        rePage.setRecords(modelTestVOList);
        return rePage;
    }

    /**
     * 检查参数
     * @param req 请求参数
     */
    public void checkParams(ModelTestCreateReq req) {
        ModelTestParamTypeEnum typeEnum = ModelTestParamTypeEnum.getByCode(req.getParamType());
        BaseBizException.isTrue(Objects.nonNull(typeEnum), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.param.type.error"));

        if (Objects.equals(typeEnum, ModelTestParamTypeEnum.SINGLE) && !JSON.isValidObject(req.getModelParams())) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.param.format.error.object"));
        }

        /*if (Objects.equals(typeEnum, ModelTestParamTypeEnum.BATCH) && !JSON.isValidArray(req.getModelParams())) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.param.format.error.array"));
        }*/

        /*String resultParam = Objects.equals(typeEnum, ModelTestParamTypeEnum.SINGLE) ?
                JSON.parseObject(req.getModelParams()).toJSONString() : JSON.parseArray(req.getModelParams()).toJSONString();
        req.setModelParams(resultParam);*/
    }

    @Override
    public void createModelTest(ModelTestCreateReq req) {
        checkParams(req);
        // 检查测试任务是否存在
        boolean exist = modelAutoTestService.checkSameModelExist(req.getTaskType(), req.getModelName());
        if (exist) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.test.already.exists"));
        }

        modelAutoTestService.saveModelAutoTest(req);
    }

    @Override
    public ModelTestVO queryByTestNo(String testNo) {
        ModelAutoTestRecord modelAutoTestRecord = modelAutoTestService.queryByTestNo(testNo);
        List<ModelAutoTestParam> modelAutoTestParams = modelAutoTestService.queryParamByTestNo(testNo);
        List<ModelTestParamVO> modelTestParamVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(modelAutoTestParams)) {
            modelTestParamVOList = modelAutoTestParams.stream()
                    .map(param -> {
                        ModelTestParamVO paramVO = new ModelTestParamVO();
                        paramVO.setId(param.getId());
                        paramVO.setTestNo(testNo);
                        paramVO.setParamName(param.getParamName());
                        paramVO.setParamFileUrl(buildAccessUrl(param.getParamFileUrl()));
                        return paramVO;
                    })
                    .collect(Collectors.toList());
        }
        ModelTestVO modelTestVO = BeanUtil.copyProperties(modelAutoTestRecord, ModelTestVO.class);
        modelTestVO.setModelTestParamVOList(modelTestParamVOList);
        return modelTestVO;
    }

    /**
     * 构建OSS文件的完整访问URL
     *
     * @param filePath OSS文件路径
     * @return 完整的访问URL
     */
    private String buildAccessUrl(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return null;
        }

        try {
            // 从配置中获取OSS信息
            String ossUrl = SpringUtils.getProperties("oss.oss_url");
            String bucketName = SpringUtils.getProperties("oss.bucket_name");

            if (StrUtil.isBlank(ossUrl) || StrUtil.isBlank(bucketName)) {
                log.warn("OSS配置不完整，无法构建访问URL");
                return null;
            }

            // 构建完整的访问URL: https://{bucket}.{endpoint}/{filePath}
            String accessUrl = String.format("https://%s.%s/%s", bucketName, ossUrl, filePath);

            log.debug("构建访问URL: {}", accessUrl);
            return accessUrl;

        } catch (Exception e) {
            log.error("构建访问URL失败，文件路径: {}", filePath, e);
            return null;
        }
    }

    @Override
    public void updateModelTest(ModelTestCreateReq req) {
        checkParams(req);
        boolean updated = modelAutoTestService.updateModelAutoTest(req);
        if (!updated) {
            throw new BaseBizException(CustomErrorCode.DATA_CHANGED);
        }
    }

    @Override
    public void startModelTest(String testNo) {
        modelAutoTestService.startModelTest(testNo);
    }

    @Override
    public void cancelModelTest(String testNo) {
        modelAutoTestService.cancelModelTest(testNo);
    }

    @Override
    public void delModelTest(String testNo) {
        modelAutoTestService.delModelTest(testNo);
    }

    @Override
    public ModelTestReportVO queryReportByTestNo(String testNo) {
        ModelAutoTestReport report = modelAutoTestService.queryReportByTestNo(testNo);
        if (Objects.isNull(report)) {
            return null;
        }

        ModelTestReportVO modelTestReportVO = BeanUtil.copyProperties(report, ModelTestReportVO.class, "taskInfo", "gpuInfo", "gpuInfoDetails", "cpuInfo", "memoryInfo", "resourceLimits");
        modelTestReportVO.setTaskInfo(JSON.parseObject(report.getTaskInfo(), ModelTestReportInfo.TaskInfo.class));
        modelTestReportVO.setGpuInfo(JSON.parseObject(report.getGpuInfo(), ModelTestReportInfo.MetricData.class));
        modelTestReportVO.setCpuInfo(JSON.parseObject(report.getCpuInfo(), ModelTestReportInfo.MetricData.class));
        modelTestReportVO.setMemoryInfo(JSON.parseObject(report.getMemoryInfo(), ModelTestReportInfo.MetricData.class));
        modelTestReportVO.setResourceLimits(JSON.parseObject(report.getResourceLimits(), ModelTestReportInfo.ResourceLimit.class));

        List<ModelTestReportInfo.MetricData> gpuInfoDetails = StringUtils.hasText(report.getGpuInfoDetails()) ? JSON.parseArray(report.getGpuInfoDetails(), ModelTestReportInfo.MetricData.class) : List.of();
        modelTestReportVO.setGpuInfoDetails(gpuInfoDetails);
        return modelTestReportVO;
    }
}
